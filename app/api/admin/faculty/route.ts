import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import bcrypt from 'bcryptjs'
import { UserRole, UserStatus } from '@prisma/client'

// Validation schema for creating faculty
const createFacultySchema = z.object({
  // User details
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Valid email is required'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  
  // Faculty profile details
  title: z.string().min(1, 'Title is required'),
  departmentId: z.string().min(1, 'Department is required'),
  officeLocation: z.string().optional(),
  websiteUrl: z.string().url().optional().or(z.literal('')),
  scholarId: z.string().optional(),
  bio: z.string().optional(),
  
  // User profile details
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  phone: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const departmentId = searchParams.get('departmentId') || ''

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      user: {
        role: UserRole.FACULTY
      }
    }

    if (search) {
      where.OR = [
        { user: { name: { contains: search, mode: 'insensitive' } } },
        { user: { email: { contains: search, mode: 'insensitive' } } },
        { title: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (departmentId) {
      where.departmentId = departmentId
    }

    const [faculty, total] = await Promise.all([
      prisma.facultyProfile.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              createdAt: true
            }
          },
          department: {
            select: {
              id: true,
              name: true
            }
          },
          _count: {
            select: {
              publications: true,
              researchAreas: true,
              classes: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.facultyProfile.count({ where })
    ])

    return NextResponse.json({
      faculty,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching faculty:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createFacultySchema.parse(body)

    // Check if email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email }
    })

    if (existingUser) {
      return NextResponse.json({ error: 'Email already exists' }, { status: 400 })
    }

    // Check if department exists
    const department = await prisma.department.findUnique({
      where: { id: validatedData.departmentId }
    })

    if (!department) {
      return NextResponse.json({ error: 'Department not found' }, { status: 400 })
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(validatedData.password, 12)

    // Create user and faculty profile in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create user
      const user = await tx.user.create({
        data: {
          name: validatedData.name,
          email: validatedData.email,
          password: hashedPassword,
          role: UserRole.FACULTY,
          status: UserStatus.ACTIVE
        }
      })

      // Create user profile if additional details provided
      if (validatedData.firstName || validatedData.lastName || validatedData.phone) {
        await tx.userProfile.create({
          data: {
            userId: user.id,
            firstName: validatedData.firstName,
            lastName: validatedData.lastName,
            phone: validatedData.phone
          }
        })
      }

      // Create faculty profile
      const facultyProfile = await tx.facultyProfile.create({
        data: {
          userId: user.id,
          title: validatedData.title,
          departmentId: validatedData.departmentId,
          officeLocation: validatedData.officeLocation,
          websiteUrl: validatedData.websiteUrl,
          scholarId: validatedData.scholarId,
          bio: validatedData.bio
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              createdAt: true
            }
          },
          department: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      return facultyProfile
    })

    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    console.error('Error creating faculty:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation error', 
        details: error.errors 
      }, { status: 400 })
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
