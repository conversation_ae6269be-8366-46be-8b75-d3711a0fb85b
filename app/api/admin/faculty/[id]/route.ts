import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for updating faculty
const updateFacultySchema = z.object({
  // User details
  name: z.string().min(1, 'Name is required').optional(),
  email: z.string().email('Valid email is required').optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'PENDING', 'SUSPENDED']).optional(),
  
  // Faculty profile details
  title: z.string().min(1, 'Title is required').optional(),
  departmentId: z.string().min(1, 'Department is required').optional(),
  officeLocation: z.string().optional(),
  websiteUrl: z.string().url().optional().or(z.literal('')),
  scholarId: z.string().optional(),
  bio: z.string().optional(),
  
  // User profile details
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  phone: z.string().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const facultyProfile = await prisma.facultyProfile.findUnique({
      where: { id: params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            status: true,
            createdAt: true,
            profile: {
              select: {
                firstName: true,
                lastName: true,
                phone: true
              }
            }
          }
        },
        department: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            publications: true,
            researchAreas: true,
            classes: true,
            officeHours: true
          }
        }
      }
    })

    if (!facultyProfile) {
      return NextResponse.json({ error: 'Faculty not found' }, { status: 404 })
    }

    return NextResponse.json(facultyProfile)
  } catch (error) {
    console.error('Error fetching faculty:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updateFacultySchema.parse(body)

    // Check if faculty exists
    const existingFaculty = await prisma.facultyProfile.findUnique({
      where: { id: params.id },
      include: { user: true }
    })

    if (!existingFaculty) {
      return NextResponse.json({ error: 'Faculty not found' }, { status: 404 })
    }

    // Check if email is being changed and if it already exists
    if (validatedData.email && validatedData.email !== existingFaculty.user.email) {
      const existingUser = await prisma.user.findUnique({
        where: { email: validatedData.email }
      })

      if (existingUser) {
        return NextResponse.json({ error: 'Email already exists' }, { status: 400 })
      }
    }

    // Check if department exists (if being changed)
    if (validatedData.departmentId) {
      const department = await prisma.department.findUnique({
        where: { id: validatedData.departmentId }
      })

      if (!department) {
        return NextResponse.json({ error: 'Department not found' }, { status: 400 })
      }
    }

    // Update in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update user if user fields are provided
      if (validatedData.name || validatedData.email || validatedData.status) {
        await tx.user.update({
          where: { id: existingFaculty.userId },
          data: {
            ...(validatedData.name && { name: validatedData.name }),
            ...(validatedData.email && { email: validatedData.email }),
            ...(validatedData.status && { status: validatedData.status as any })
          }
        })
      }

      // Update or create user profile if profile fields are provided
      if (validatedData.firstName || validatedData.lastName || validatedData.phone) {
        await tx.userProfile.upsert({
          where: { userId: existingFaculty.userId },
          update: {
            ...(validatedData.firstName !== undefined && { firstName: validatedData.firstName }),
            ...(validatedData.lastName !== undefined && { lastName: validatedData.lastName }),
            ...(validatedData.phone !== undefined && { phone: validatedData.phone })
          },
          create: {
            userId: existingFaculty.userId,
            firstName: validatedData.firstName,
            lastName: validatedData.lastName,
            phone: validatedData.phone
          }
        })
      }

      // Update faculty profile
      const facultyProfile = await tx.facultyProfile.update({
        where: { id: params.id },
        data: {
          ...(validatedData.title && { title: validatedData.title }),
          ...(validatedData.departmentId && { departmentId: validatedData.departmentId }),
          ...(validatedData.officeLocation !== undefined && { officeLocation: validatedData.officeLocation }),
          ...(validatedData.websiteUrl !== undefined && { websiteUrl: validatedData.websiteUrl }),
          ...(validatedData.scholarId !== undefined && { scholarId: validatedData.scholarId }),
          ...(validatedData.bio !== undefined && { bio: validatedData.bio })
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              createdAt: true
            }
          },
          department: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      return facultyProfile
    })

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error updating faculty:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation error', 
        details: error.errors 
      }, { status: 400 })
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    // Check if user is admin
    if (!session?.user || !['COLLEGE_ADMIN', 'SYS_ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if faculty exists
    const existingFaculty = await prisma.facultyProfile.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            classes: true,
            officeHours: true,
            researchProjects: true
          }
        }
      }
    })

    if (!existingFaculty) {
      return NextResponse.json({ error: 'Faculty not found' }, { status: 404 })
    }

    // Check for dependencies
    const hasActiveDependencies = 
      existingFaculty._count.classes > 0 ||
      existingFaculty._count.officeHours > 0 ||
      existingFaculty._count.researchProjects > 0

    if (hasActiveDependencies) {
      return NextResponse.json({ 
        error: 'Cannot delete faculty with active classes, office hours, or research projects',
        dependencies: {
          classes: existingFaculty._count.classes,
          officeHours: existingFaculty._count.officeHours,
          researchProjects: existingFaculty._count.researchProjects
        }
      }, { status: 400 })
    }

    // Delete faculty profile (this will cascade to related data)
    await prisma.facultyProfile.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Faculty deleted successfully' })
  } catch (error) {
    console.error('Error deleting faculty:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
