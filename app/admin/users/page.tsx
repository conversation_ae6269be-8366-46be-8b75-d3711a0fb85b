import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Users, Plus, Search, Filter, MoreHorizontal, <PERSON>r<PERSON><PERSON>ck, <PERSON>r<PERSON>, <PERSON> } from "lucide-react"
import { prisma } from "@/lib/prisma"
import { requireAdmin } from "@/lib/auth-utils"
import { UserRole, UserStatus } from "@prisma/client"
import Link from "next/link"

async function getUsersData() {
  const [users, roleStats, statusStats] = await Promise.all([
    prisma.user.findMany({
      include: {
        profile: {
          select: {
            firstName: true,
            lastName: true,
            phone: true
          }
        },
        facultyProfile: {
          select: {
            title: true,
            department: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 50 // Limit for performance
    }),
    // Count users by role
    Promise.all([
      prisma.user.count({ where: { role: UserRole.STUDENT } }),
      prisma.user.count({ where: { role: UserRole.FACULTY } }),
      prisma.user.count({ where: { role: UserRole.COLLEGE_ADMIN } }),
      prisma.user.count({ where: { role: UserRole.SYS_ADMIN } })
    ]),
    // Count users by status
    Promise.all([
      prisma.user.count({ where: { status: UserStatus.ACTIVE } }),
      prisma.user.count({ where: { status: UserStatus.PENDING } }),
      prisma.user.count({ where: { status: UserStatus.INACTIVE } }),
      prisma.user.count({ where: { status: UserStatus.SUSPENDED } })
    ])
  ])

  const [studentCount, facultyCount, collegeAdminCount, sysAdminCount] = roleStats
  const [activeCount, pendingCount, inactiveCount, suspendedCount] = statusStats

  return {
    users,
    totalUsers: users.length,
    roleStats: {
      STUDENT: studentCount,
      FACULTY: facultyCount,
      COLLEGE_ADMIN: collegeAdminCount,
      SYS_ADMIN: sysAdminCount
    },
    statusStats: {
      ACTIVE: activeCount,
      PENDING: pendingCount,
      INACTIVE: inactiveCount,
      SUSPENDED: suspendedCount
    }
  }
}

function getRoleColor(role: UserRole) {
  switch (role) {
    case UserRole.SYS_ADMIN:
      return 'bg-red-100 text-red-800'
    case UserRole.COLLEGE_ADMIN:
      return 'bg-purple-100 text-purple-800'
    case UserRole.FACULTY:
      return 'bg-blue-100 text-blue-800'
    case UserRole.STUDENT:
      return 'bg-green-100 text-green-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

function getStatusColor(status: UserStatus) {
  switch (status) {
    case UserStatus.ACTIVE:
      return 'bg-green-100 text-green-800'
    case UserStatus.PENDING:
      return 'bg-yellow-100 text-yellow-800'
    case UserStatus.INACTIVE:
      return 'bg-gray-100 text-gray-800'
    case UserStatus.SUSPENDED:
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

export default async function UserManagementPage() {
  // Require admin access
  await requireAdmin()
  
  const data = await getUsersData()

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">User Management</h1>
          <p className="text-gray-600">Manage user accounts and permissions</p>
        </div>
        <Button asChild>
          <Link href="/admin/users/new">
            <Plus className="w-4 h-4 mr-2" />
            Add User
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalUsers}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <UserCheck className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.statusStats.ACTIVE}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Users</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.statusStats.PENDING}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Faculty</CardTitle>
            <Users className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.roleStats.FACULTY}</div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>User Directory</CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.users.map((user) => (
              <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-medium">{user.name || 'No Name'}</h3>
                    <p className="text-sm text-gray-500">{user.email}</p>
                    {user.facultyProfile && (
                      <p className="text-sm text-gray-500">
                        {user.facultyProfile.title} - {user.facultyProfile.department.name}
                      </p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="text-right text-sm">
                    <p className="text-gray-600">
                      Created: {new Date(user.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                  
                  <Badge className={getRoleColor(user.role)}>
                    {user.role.replace('_', ' ')}
                  </Badge>
                  
                  <Badge className={getStatusColor(user.status)}>
                    {user.status}
                  </Badge>
                  
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
            
            {data.users.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No users found</p>
                <Button asChild className="mt-4">
                  <Link href="/admin/users/new">Add First User</Link>
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Role Distribution */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Users by Role</CardTitle>
            <CardDescription>Distribution of user roles</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span>Students</span>
                <span className="font-bold">{data.roleStats.STUDENT}</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Faculty</span>
                <span className="font-bold">{data.roleStats.FACULTY}</span>
              </div>
              <div className="flex justify-between items-center">
                <span>College Admins</span>
                <span className="font-bold">{data.roleStats.COLLEGE_ADMIN}</span>
              </div>
              <div className="flex justify-between items-center">
                <span>System Admins</span>
                <span className="font-bold">{data.roleStats.SYS_ADMIN}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Users by Status</CardTitle>
            <CardDescription>User account status distribution</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span>Active</span>
                <span className="font-bold text-green-600">{data.statusStats.ACTIVE}</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Pending</span>
                <span className="font-bold text-yellow-600">{data.statusStats.PENDING}</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Inactive</span>
                <span className="font-bold text-gray-600">{data.statusStats.INACTIVE}</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Suspended</span>
                <span className="font-bold text-red-600">{data.statusStats.SUSPENDED}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
