import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { BookOpen, Plus, Search, Filter, MoreHorizontal, Building2, Clock, GraduationCap } from "lucide-react"
import { prisma } from "@/lib/prisma"
import { requireAdmin } from "@/lib/auth-utils"
import Link from "next/link"

async function getProgramsData() {
  const [programs, departments] = await Promise.all([
    prisma.program.findMany({
      include: {
        department: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    }),
    prisma.department.findMany({
      include: {
        _count: {
          select: {
            programs: true
          }
        }
      }
    })
  ])

  // Group programs by degree type
  const programsByDegree = programs.reduce((acc, program) => {
    if (!acc[program.degreeType]) {
      acc[program.degreeType] = []
    }
    acc[program.degreeType].push(program)
    return acc
  }, {} as Record<string, typeof programs>)

  const activePrograms = programs.filter(p => p.isActive).length
  const avgDuration = programs.length > 0 
    ? Math.round(programs.reduce((sum, p) => sum + p.duration, 0) / programs.length * 10) / 10
    : 0

  return {
    programs,
    departments,
    programsByDegree,
    totalPrograms: programs.length,
    activePrograms,
    avgDuration
  }
}

function getDegreeTypeColor(degreeType: string) {
  const colors: Record<string, string> = {
    'Bachelor': 'bg-blue-100 text-blue-800',
    'Master': 'bg-green-100 text-green-800',
    'PhD': 'bg-purple-100 text-purple-800',
    'Diploma': 'bg-orange-100 text-orange-800',
    'Certificate': 'bg-yellow-100 text-yellow-800'
  }
  return colors[degreeType] || 'bg-gray-100 text-gray-800'
}

export default async function ProgramsPage() {
  // Require admin access
  await requireAdmin()
  
  const data = await getProgramsData()

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Academic Programs</h1>
          <p className="text-gray-600">Manage degree programs and academic offerings</p>
        </div>
        <Button asChild>
          <Link href="/admin/programs/new">
            <Plus className="w-4 h-4 mr-2" />
            Add Program
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Programs</CardTitle>
            <BookOpen className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalPrograms}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Programs</CardTitle>
            <GraduationCap className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.activePrograms}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Departments</CardTitle>
            <Building2 className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.departments.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Duration</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.avgDuration} years</div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Program Directory</CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.programs.map((program) => (
              <div key={program.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <BookOpen className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-medium">{program.name}</h3>
                    <p className="text-sm text-gray-500">{program.department.name}</p>
                    <p className="text-sm text-gray-500">
                      {program.duration} year{program.duration !== 1 ? 's' : ''} • {program.degreeType}
                    </p>
                    {program.description && (
                      <p className="text-sm text-gray-400 mt-1 line-clamp-2">
                        {program.description}
                      </p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="text-right text-sm">
                    <p className="text-gray-600">
                      Created: {new Date(program.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                  
                  <Badge className={getDegreeTypeColor(program.degreeType)}>
                    {program.degreeType}
                  </Badge>
                  
                  <Badge variant={program.isActive ? 'default' : 'secondary'}>
                    {program.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                  
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
            
            {data.programs.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <BookOpen className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No programs found</p>
                <Button asChild className="mt-4">
                  <Link href="/admin/programs/new">Create First Program</Link>
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Programs by Degree Type */}
      <Card>
        <CardHeader>
          <CardTitle>Programs by Degree Type</CardTitle>
          <CardDescription>Distribution of programs across degree levels</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(data.programsByDegree).map(([degreeType, programs]) => (
              <div key={degreeType} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{degreeType}</h4>
                  <Badge className={getDegreeTypeColor(degreeType)}>
                    {programs.length}
                  </Badge>
                </div>
                <div className="space-y-1">
                  {programs.slice(0, 3).map((program) => (
                    <div key={program.id} className="text-sm text-gray-600">
                      {program.name}
                    </div>
                  ))}
                  {programs.length > 3 && (
                    <div className="text-sm text-gray-500">
                      +{programs.length - 3} more
                    </div>
                  )}
                </div>
              </div>
            ))}
            
            {Object.keys(data.programsByDegree).length === 0 && (
              <div className="col-span-full text-center py-4 text-gray-500">
                <p>No programs found</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Programs by Department */}
      <Card>
        <CardHeader>
          <CardTitle>Programs by Department</CardTitle>
          <CardDescription>Program distribution across departments</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {data.departments.map((department) => (
              <div key={department.id} className="p-4 border rounded-lg">
                <h4 className="font-medium">{department.name}</h4>
                <p className="text-2xl font-bold text-blue-600">{department._count.programs}</p>
                <p className="text-sm text-gray-500">programs</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common program management tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/admin/programs/new" className="flex flex-col items-center space-y-2">
                <BookOpen className="w-8 h-8 text-blue-600" />
                <div className="text-center">
                  <h3 className="font-medium">New Program</h3>
                  <p className="text-sm text-gray-500">Create degree program</p>
                </div>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/admin/departments" className="flex flex-col items-center space-y-2">
                <Building2 className="w-8 h-8 text-green-600" />
                <div className="text-center">
                  <h3 className="font-medium">Manage Departments</h3>
                  <p className="text-sm text-gray-500">View departments</p>
                </div>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/admin/programs?filter=inactive" className="flex flex-col items-center space-y-2">
                <GraduationCap className="w-8 h-8 text-orange-600" />
                <div className="text-center">
                  <h3 className="font-medium">Review Inactive</h3>
                  <p className="text-sm text-gray-500">Inactive programs</p>
                </div>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
