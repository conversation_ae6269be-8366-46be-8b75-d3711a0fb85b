import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Building2, Plus, Search, Filter, MoreHorizontal, Users, BookOpen, Calendar, Edit, Trash2 } from "lucide-react"
import { prisma } from "@/lib/prisma"
import { requireAdmin } from "@/lib/auth-utils"
import Link from "next/link"
import { DepartmentActions } from "@/components/admin/DepartmentActions"

async function getDepartmentsData() {
  const departments = await prisma.department.findMany({
    include: {
      _count: {
        select: {
          faculty: true,
          programs: true,
          courses: true
        }
      },
      faculty: {
        take: 3, // Get first 3 faculty for preview
        include: {
          user: {
            select: {
              name: true,
              email: true
            }
          }
        }
      },
      programs: {
        take: 3, // Get first 3 programs for preview
        select: {
          name: true,
          degreeType: true
        }
      }
    },
    orderBy: {
      name: 'asc'
    }
  })

  const totalFaculty = departments.reduce((sum, dept) => sum + dept._count.faculty, 0)
  const totalPrograms = departments.reduce((sum, dept) => sum + dept._count.programs, 0)
  const totalCourses = departments.reduce((sum, dept) => sum + dept._count.courses, 0)

  return {
    departments,
    totalDepartments: departments.length,
    totalFaculty,
    totalPrograms,
    totalCourses
  }
}

export default async function DepartmentsPage() {
  // Require admin access
  await requireAdmin()

  const data = await getDepartmentsData()

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Departments</h1>
          <p className="text-gray-600">Manage academic departments and their structure</p>
        </div>
        <Button asChild>
          <Link href="/admin/departments/new">
            <Plus className="w-4 h-4 mr-2" />
            Add Department
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Departments</CardTitle>
            <Building2 className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalDepartments}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Faculty</CardTitle>
            <Users className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalFaculty}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Programs</CardTitle>
            <BookOpen className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalPrograms}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Courses</CardTitle>
            <Calendar className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalCourses}</div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Department Directory</CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {data.departments.map((department) => (
              <Card key={department.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{department.name}</CardTitle>
                      <CardDescription className="mt-1">
                        {department.description || 'No description available'}
                      </CardDescription>
                    </div>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{department._count.faculty}</div>
                      <div className="text-xs text-gray-500">Faculty</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{department._count.programs}</div>
                      <div className="text-xs text-gray-500">Programs</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">{department._count.courses}</div>
                      <div className="text-xs text-gray-500">Courses</div>
                    </div>
                  </div>

                  {/* Faculty Preview */}
                  {department.faculty.length > 0 && (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium mb-2">Faculty</h4>
                      <div className="space-y-1">
                        {department.faculty.map((faculty) => (
                          <div key={faculty.id} className="text-sm text-gray-600">
                            {faculty.user.name || faculty.user.email}
                          </div>
                        ))}
                        {department._count.faculty > 3 && (
                          <div className="text-sm text-gray-500">
                            +{department._count.faculty - 3} more
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Programs Preview */}
                  {department.programs.length > 0 && (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium mb-2">Programs</h4>
                      <div className="flex flex-wrap gap-1">
                        {department.programs.map((program, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {program.degreeType}
                          </Badge>
                        ))}
                        {department._count.programs > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{department._count.programs - 3} more
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-2 border-t">
                    <Button asChild variant="outline" size="sm" className="flex-1">
                      <Link href={`/admin/departments/${department.id}/edit`}>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit
                      </Link>
                    </Button>
                    <DepartmentActions
                      departmentId={department.id}
                      departmentName={department.name}
                      dependencies={{
                        faculty: department._count.faculty,
                        programs: department._count.programs,
                        courses: department._count.courses
                      }}
                    />
                  </div>
                </CardContent>
              </Card>
            ))}

            {data.departments.length === 0 && (
              <div className="col-span-full text-center py-8 text-gray-500">
                <Building2 className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No departments found</p>
                <Button asChild className="mt-4">
                  <Link href="/admin/departments/new">Create First Department</Link>
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common department management tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/admin/departments/new" className="flex flex-col items-center space-y-2">
                <Building2 className="w-8 h-8 text-blue-600" />
                <div className="text-center">
                  <h3 className="font-medium">New Department</h3>
                  <p className="text-sm text-gray-500">Create academic department</p>
                </div>
              </Link>
            </Button>

            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/admin/programs" className="flex flex-col items-center space-y-2">
                <BookOpen className="w-8 h-8 text-green-600" />
                <div className="text-center">
                  <h3 className="font-medium">Manage Programs</h3>
                  <p className="text-sm text-gray-500">View degree programs</p>
                </div>
              </Link>
            </Button>

            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/admin/courses" className="flex flex-col items-center space-y-2">
                <Calendar className="w-8 h-8 text-purple-600" />
                <div className="text-center">
                  <h3 className="font-medium">Manage Courses</h3>
                  <p className="text-sm text-gray-500">View course catalog</p>
                </div>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
