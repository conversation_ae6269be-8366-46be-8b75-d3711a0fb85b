import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Users, Plus, Search, Filter, MoreHorizontal, Mail, Phone, MapPin } from "lucide-react"
import { prisma } from "@/lib/prisma"
import { requireAdmin } from "@/lib/auth-utils"
import Link from "next/link"

async function getFacultyData() {
  const [facultyList, departmentStats] = await Promise.all([
    prisma.facultyProfile.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            status: true,
            createdAt: true
          }
        },
        department: {
          select: {
            name: true
          }
        },
        _count: {
          select: {
            publications: true,
            researchAreas: true,
            classes: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    }),
    prisma.department.findMany({
      include: {
        _count: {
          select: {
            faculty: true
          }
        }
      }
    })
  ])

  return {
    facultyList,
    departmentStats,
    totalFaculty: facultyList.length,
    activeFaculty: facultyList.filter(f => f.user.status === 'ACTIVE').length
  }
}

export default async function FacultyManagementPage() {
  // Require admin access
  await requireAdmin()
  
  const data = await getFacultyData()

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Faculty Management</h1>
          <p className="text-gray-600">Manage faculty profiles and information</p>
        </div>
        <Button asChild>
          <Link href="/admin/faculty/new">
            <Plus className="w-4 h-4 mr-2" />
            Add Faculty
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Faculty</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalFaculty}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Faculty</CardTitle>
            <Users className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.activeFaculty}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Departments</CardTitle>
            <Users className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.departmentStats.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Publications</CardTitle>
            <Users className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {data.facultyList.length > 0 
                ? Math.round(data.facultyList.reduce((sum, f) => sum + f._count.publications, 0) / data.facultyList.length)
                : 0
              }
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Faculty Directory</CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.facultyList.map((faculty) => (
              <div key={faculty.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-medium">{faculty.user.name || 'No Name'}</h3>
                    <p className="text-sm text-gray-500">{faculty.title}</p>
                    <p className="text-sm text-gray-500">{faculty.department.name}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="text-right text-sm">
                    <p className="text-gray-600">{faculty._count.publications} publications</p>
                    <p className="text-gray-600">{faculty._count.classes} classes</p>
                  </div>
                  
                  <Badge variant={faculty.user.status === 'ACTIVE' ? 'default' : 'secondary'}>
                    {faculty.user.status}
                  </Badge>
                  
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
            
            {data.facultyList.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No faculty members found</p>
                <Button asChild className="mt-4">
                  <Link href="/admin/faculty/new">Add First Faculty Member</Link>
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Department Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Faculty by Department</CardTitle>
          <CardDescription>Distribution of faculty across departments</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {data.departmentStats.map((dept) => (
              <div key={dept.id} className="p-4 border rounded-lg">
                <h4 className="font-medium">{dept.name}</h4>
                <p className="text-2xl font-bold text-blue-600">{dept._count.faculty}</p>
                <p className="text-sm text-gray-500">faculty members</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
