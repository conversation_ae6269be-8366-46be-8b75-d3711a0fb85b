import { Card, Card<PERSON>ontent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, Plus, Search, Filter, MoreHorizontal, Building2, BookOpen, Users } from "lucide-react"
import { prisma } from "@/lib/prisma"
import { requireAdmin } from "@/lib/auth-utils"
import Link from "next/link"

async function getCoursesData() {
  const [courses, departments, courseClasses] = await Promise.all([
    prisma.course.findMany({
      include: {
        department: {
          select: {
            name: true
          }
        },
        _count: {
          select: {
            classes: true
          }
        }
      },
      orderBy: {
        code: 'asc'
      }
    }),
    prisma.department.findMany({
      include: {
        _count: {
          select: {
            courses: true
          }
        }
      }
    }),
    prisma.courseClass.findMany({
      include: {
        course: {
          select: {
            name: true,
            code: true
          }
        },
        faculty: {
          include: {
            user: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        year: 'desc'
      },
      take: 10 // Recent classes
    })
  ])

  const activeCourses = courses.filter(c => c.isActive).length
  const totalCredits = courses.reduce((sum, course) => sum + course.credits, 0)
  const avgCredits = courses.length > 0 ? Math.round(totalCredits / courses.length * 10) / 10 : 0

  // Group courses by credit hours
  const coursesByCredits = courses.reduce((acc, course) => {
    const credits = course.credits.toString()
    if (!acc[credits]) {
      acc[credits] = 0
    }
    acc[credits]++
    return acc
  }, {} as Record<string, number>)

  return {
    courses,
    departments,
    courseClasses,
    totalCourses: courses.length,
    activeCourses,
    totalCredits,
    avgCredits,
    coursesByCredits
  }
}

function getCreditColor(credits: number) {
  if (credits <= 2) return 'bg-blue-100 text-blue-800'
  if (credits <= 3) return 'bg-green-100 text-green-800'
  if (credits <= 4) return 'bg-yellow-100 text-yellow-800'
  return 'bg-red-100 text-red-800'
}

export default async function CoursesPage() {
  // Require admin access
  await requireAdmin()
  
  const data = await getCoursesData()

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Course Catalog</h1>
          <p className="text-gray-600">Manage courses and class schedules</p>
        </div>
        <Button asChild>
          <Link href="/admin/courses/new">
            <Plus className="w-4 h-4 mr-2" />
            Add Course
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Courses</CardTitle>
            <Calendar className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalCourses}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Courses</CardTitle>
            <BookOpen className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.activeCourses}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Credits</CardTitle>
            <Building2 className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalCredits}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Credits</CardTitle>
            <Users className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.avgCredits}</div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Course Directory</CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.courses.map((course) => (
              <div key={course.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <Calendar className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-medium">{course.code} - {course.name}</h3>
                    <p className="text-sm text-gray-500">{course.department.name}</p>
                    {course.description && (
                      <p className="text-sm text-gray-400 mt-1 line-clamp-2">
                        {course.description}
                      </p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="text-right text-sm">
                    <p className="text-gray-600">{course._count.classes} classes</p>
                    <p className="text-gray-600">
                      Created: {new Date(course.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                  
                  <Badge className={getCreditColor(course.credits)}>
                    {course.credits} credits
                  </Badge>
                  
                  <Badge variant={course.isActive ? 'default' : 'secondary'}>
                    {course.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                  
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
            
            {data.courses.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Calendar className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No courses found</p>
                <Button asChild className="mt-4">
                  <Link href="/admin/courses/new">Create First Course</Link>
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Recent Classes */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Course Classes</CardTitle>
          <CardDescription>Latest scheduled course instances</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {data.courseClasses.map((courseClass) => (
              <div key={courseClass.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <h4 className="font-medium">
                    {courseClass.course.code} - {courseClass.course.name}
                  </h4>
                  <p className="text-sm text-gray-500">
                    {courseClass.faculty.user.name} • {courseClass.semester} {courseClass.year}
                    {courseClass.section && ` • Section ${courseClass.section}`}
                  </p>
                </div>
                <div className="text-right text-sm">
                  <p className="text-gray-600">
                    {courseClass.enrollmentCount}/{courseClass.maxEnrollment} enrolled
                  </p>
                  <Badge variant="outline" className="text-xs">
                    {courseClass.status}
                  </Badge>
                </div>
              </div>
            ))}
            
            {data.courseClasses.length === 0 && (
              <div className="text-center py-4 text-gray-500">
                <p>No recent classes found</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Courses by Department */}
      <Card>
        <CardHeader>
          <CardTitle>Courses by Department</CardTitle>
          <CardDescription>Course distribution across departments</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {data.departments.map((department) => (
              <div key={department.id} className="p-4 border rounded-lg">
                <h4 className="font-medium">{department.name}</h4>
                <p className="text-2xl font-bold text-blue-600">{department._count.courses}</p>
                <p className="text-sm text-gray-500">courses</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common course management tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/admin/courses/new" className="flex flex-col items-center space-y-2">
                <Calendar className="w-8 h-8 text-blue-600" />
                <div className="text-center">
                  <h3 className="font-medium">New Course</h3>
                  <p className="text-sm text-gray-500">Add to catalog</p>
                </div>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/admin/classes" className="flex flex-col items-center space-y-2">
                <Users className="w-8 h-8 text-green-600" />
                <div className="text-center">
                  <h3 className="font-medium">Manage Classes</h3>
                  <p className="text-sm text-gray-500">Schedule & enrollment</p>
                </div>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/admin/courses?filter=inactive" className="flex flex-col items-center space-y-2">
                <BookOpen className="w-8 h-8 text-orange-600" />
                <div className="text-center">
                  <h3 className="font-medium">Review Inactive</h3>
                  <p className="text-sm text-gray-500">Inactive courses</p>
                </div>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
