import Link from 'next/link'
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLink } from "@/components/ui/skip-link"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  Mail,
  Globe,
  BookOpen,
  Award,
  FileText,
  Briefcase,
  Clock,
  FileDown,
  UserPlus,
  Menu,
  ChevronRight
} from "lucide-react"
import { LazyImage } from "@/components/ui/lazy-image"

// Import faculty data types and helper functions
import {
  FacultyMember,
  getFacultyById,
  getResearchAreasForFaculty,
  facultyData
} from "@/lib/data/faculty"

// Import shared components
import { AcademicTimeline } from "@/components/ui/academic-timeline"
import { CVDownload } from "@/components/ui/cv-download"
import { ScholarlyPublications } from "@/components/ui/scholarly-publications"
import { UpcomingClasses } from "@/components/ui/upcoming-classes"
import { OfficeHoursScheduler } from "@/components/ui/office-hours-scheduler"
import { ResearchOpportunities } from "@/components/ui/research-opportunities"

// Import new components
import { Breadcrumb, BreadcrumbItem } from "@/components/ui/breadcrumb"
import { PrintButton } from "@/components/ui/print-button"
import { PrintStyles } from "@/components/ui/print-styles"
import { FavoriteButton } from "@/components/ui/favorite-button"

interface FacultyPageProps {
  params: {
    id: string
  }
}

export default async function FacultyProfilePage({ params }: FacultyPageProps) {
  const { id } = await params;
  const faculty = await getFacultyById(id);

  if (!faculty) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md p-8">
            <h1 className="text-xl font-medium mb-2 text-gray-900">Faculty Not Found</h1>
            <p className="mb-6 text-muted-foreground text-sm">
              We couldn't find the faculty member you're looking for. They may have moved to a different department or the URL might be incorrect.
            </p>
            <Link href="/faculty">
              <Button variant="outline" size="sm" className="h-9 px-4 border-crimson/20 hover:bg-crimson/5">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Faculty Directory
              </Button>
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Build breadcrumb items
  const breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Faculty', href: '/faculty' },
    { label: faculty.department, href: `/faculty?department=${encodeURIComponent(faculty.department)}` },
    { label: faculty.name, href: `/faculty/${faculty.id}`, current: true }
  ];

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <PrintStyles selector="#print-content" />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          {/* Breadcrumb Navigation */}
          <div className="bg-gradient-to-b from-light/50 to-background/50">
            <div className="container mx-auto px-4 max-w-6xl py-4">
              <Breadcrumb items={breadcrumbItems} />
            </div>
          </div>

          {/* Faculty Profile Header with gradient background */}
          <section className="py-8 bg-gradient-to-b from-background to-light/30 relative overflow-hidden">
            {/* Subtle background decorative elements */}
            <div className="absolute inset-0 overflow-hidden opacity-10">
              <div className="absolute -top-20 -left-20 w-96 h-96 bg-crimson rounded-full blur-3xl"></div>
              <div className="absolute bottom-0 right-0 w-80 h-80 bg-gold rounded-full blur-3xl"></div>
            </div>

            <div className="container mx-auto px-4 max-w-6xl relative">
              <div className="flex justify-between items-center mb-8">
                <Link href="/faculty" aria-label="Back to Faculty Directory">
                  <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900 p-2 h-8">
                    <ArrowLeft className="h-4 w-4 mr-1" />
                    <span className="text-sm">Back</span>
                  </Button>
                </Link>

                <div className="flex items-center gap-2 print-hide">
                  <PrintButton
                    variant="ghost"
                    size="sm"
                    className="text-gray-600 h-8"
                  />
                </div>
              </div>

              <div id="print-content" className="flex flex-col md:flex-row gap-8 mb-8">
                <div className="md:w-1/3 lg:w-1/4 max-w-[240px] mx-auto md:mx-0">
                  <div className="overflow-hidden rounded-md border shadow-sm">
                    <LazyImage
                      src={faculty.imageUrl}
                      alt={faculty.altText}
                      aspectRatio="aspect-[3/4]"
                      className="h-full w-full object-cover"
                    />
                  </div>

                  {/* Contact info for mobile view */}
                  <div className="md:hidden mt-4 space-y-2">
                    <a
                      href={`mailto:${faculty.email}`}
                      className="flex items-center text-sm text-gray-600 hover:text-crimson"
                    >
                      <Mail className="h-4 w-4 mr-2 text-gray-400" />
                      {faculty.email}
                    </a>
                    <a
                      href={faculty.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-sm text-gray-600 hover:text-crimson"
                    >
                      <Globe className="h-4 w-4 mr-2 text-gray-400" />
                      Website
                    </a>
                  </div>
                </div>

                <div className="md:w-2/3 lg:w-3/4">
                  <div className="inline-flex items-center px-2.5 py-0.5 rounded-full bg-crimson/10 text-crimson text-xs font-medium mb-2">
                    {faculty.department}
                  </div>
                  <h1 className="text-2xl font-medium text-gray-900 mb-2">
                    {faculty.name}
                  </h1>
                  <p className="text-base text-muted-foreground mb-4">
                    {faculty.title}
                  </p>

                  {/* Contact info for desktop */}
                  <div className="hidden md:flex items-center gap-4 mb-6">
                    <a
                      href={`mailto:${faculty.email}`}
                      className="flex items-center text-sm text-gray-600 hover:text-crimson"
                    >
                      <Mail className="h-4 w-4 mr-2 text-gray-400" />
                      {faculty.email}
                    </a>
                    <a
                      href={faculty.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-sm text-gray-600 hover:text-crimson"
                    >
                      <Globe className="h-4 w-4 mr-2 text-gray-400" />
                      Website
                    </a>
                  </div>

                  <p className="text-muted-foreground text-sm leading-relaxed">
                    {faculty.bio}
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* Main Content Area */}
          <section className="py-8">
            <div className="container mx-auto px-4 max-w-6xl">
              <Tabs defaultValue="education" className="w-full">
                <TabsList className="grid w-full grid-cols-4 lg:grid-cols-6 mb-8">
                  <TabsTrigger value="education" className="text-xs">
                    <BookOpen className="h-4 w-4 mr-1" />
                    <span className="hidden sm:inline">Education</span>
                  </TabsTrigger>
                  <TabsTrigger value="research" className="text-xs">
                    <Award className="h-4 w-4 mr-1" />
                    <span className="hidden sm:inline">Research</span>
                  </TabsTrigger>
                  <TabsTrigger value="publications" className="text-xs">
                    <FileText className="h-4 w-4 mr-1" />
                    <span className="hidden sm:inline">Publications</span>
                  </TabsTrigger>
                  <TabsTrigger value="courses" className="text-xs">
                    <Briefcase className="h-4 w-4 mr-1" />
                    <span className="hidden sm:inline">Courses</span>
                  </TabsTrigger>
                  <TabsTrigger value="schedule" className="text-xs">
                    <Clock className="h-4 w-4 mr-1" />
                    <span className="hidden sm:inline">Schedule</span>
                  </TabsTrigger>
                  <TabsTrigger value="opportunities" className="text-xs">
                    <UserPlus className="h-4 w-4 mr-1" />
                    <span className="hidden sm:inline">Opportunities</span>
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="education" className="space-y-6">
                  <Card>
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold mb-4 flex items-center">
                        <BookOpen className="h-5 w-5 mr-2" />
                        Academic Background
                      </h3>
                      <ul className="space-y-2">
                        {faculty.education.map((edu, index) => (
                          <li key={index} className="text-sm text-muted-foreground">
                            {edu}
                          </li>
                        ))}
                      </ul>

                      {faculty.timeline && (
                        <div className="mt-6">
                          <div className="space-y-4">
                            <h4 className="font-medium">Academic Timeline</h4>
                            <div className="space-y-3">
                              {faculty.timeline.map((event) => (
                                <div key={event.id} className="border-l-2 border-crimson/20 pl-4">
                                  <div className="flex items-center gap-2 mb-1">
                                    <span className="text-sm font-medium text-crimson">{event.year}</span>
                                    <span className="text-xs px-2 py-1 rounded bg-gray-100 text-gray-600">
                                      {event.type}
                                    </span>
                                  </div>
                                  <h5 className="font-medium text-sm">{event.title}</h5>
                                  <p className="text-xs text-muted-foreground">{event.description}</p>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}

                      {faculty.cvDocuments && faculty.cvDocuments.length > 0 && (
                        <div className="mt-6 pt-6 border-t">
                          <h4 className="font-medium mb-3 flex items-center">
                            <FileDown className="h-4 w-4 mr-2" />
                            Download CV & Documents
                          </h4>
                          <div className="space-y-2">
                            {faculty.cvDocuments.map((doc) => (
                              <div key={doc.id} className="flex items-center justify-between p-3 border rounded-lg">
                                <div className="flex items-center space-x-3">
                                  <FileText className="h-5 w-5 text-gray-400" />
                                  <div>
                                    <p className="text-sm font-medium">{doc.title}</p>
                                    <p className="text-xs text-muted-foreground">
                                      {doc.fileType.toUpperCase()} • {doc.fileSize} • Updated {doc.lastUpdated}
                                    </p>
                                  </div>
                                </div>
                                <Button size="sm" variant="outline" asChild>
                                  <a href={doc.url} download>
                                    <FileDown className="h-4 w-4 mr-2" />
                                    Download
                                  </a>
                                </Button>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="research" className="space-y-6">
                  <Card>
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold mb-4 flex items-center">
                        <Award className="h-5 w-5 mr-2" />
                        Research Areas
                      </h3>
                      <ul className="space-y-2">
                        {faculty.research.map((area, index) => (
                          <li key={index} className="text-sm text-muted-foreground">
                            {area}
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="publications" className="space-y-6">
                  <Card>
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold mb-4 flex items-center">
                        <FileText className="h-5 w-5 mr-2" />
                        Publications
                      </h3>
                      {faculty.scholarlyPublications && faculty.scholarlyPublications.length > 0 ? (
                        <div className="space-y-4">
                          {faculty.scholarlyPublications.map((pub) => (
                            <div key={pub.id} className="border rounded-lg p-4">
                              <h4 className="font-medium text-sm mb-2">{pub.title}</h4>
                              <p className="text-xs text-muted-foreground mb-2">
                                {pub.authors.join(', ')} • {pub.journal} • {pub.year}
                              </p>
                              {pub.citationCount && (
                                <p className="text-xs text-muted-foreground mb-2">
                                  Citations: {pub.citationCount}
                                </p>
                              )}
                              {pub.link && (
                                <a
                                  href={pub.link}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-xs text-crimson hover:underline"
                                >
                                  View Publication →
                                </a>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <ul className="space-y-2">
                          {faculty.publications.map((pub, index) => (
                            <li key={index} className="text-sm text-muted-foreground">
                              {pub}
                            </li>
                          ))}
                        </ul>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="courses" className="space-y-6">
                  <Card>
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold mb-4 flex items-center">
                        <Briefcase className="h-5 w-5 mr-2" />
                        Courses
                      </h3>
                      {faculty.courses && (
                        <ul className="space-y-2">
                          {faculty.courses.map((course, index) => (
                            <li key={index} className="text-sm text-muted-foreground">
                              {course}
                            </li>
                          ))}
                        </ul>
                      )}

                      {faculty.upcomingClasses && faculty.upcomingClasses.length > 0 && (
                        <div className="mt-6 pt-6 border-t">
                          <h4 className="font-medium mb-3">Upcoming Classes</h4>
                          <div className="space-y-3">
                            {faculty.upcomingClasses.map((classItem) => (
                              <div key={classItem.id} className="border rounded-lg p-4">
                                <div className="flex justify-between items-start mb-2">
                                  <h5 className="font-medium text-sm">{classItem.courseCode}: {classItem.courseName}</h5>
                                  <span className="text-xs px-2 py-1 rounded bg-blue-100 text-blue-700">
                                    {classItem.semester}
                                  </span>
                                </div>
                                <div className="text-xs text-muted-foreground space-y-1">
                                  <p>📅 {classItem.schedule.days.join(', ')} at {classItem.schedule.time}</p>
                                  <p>📍 {classItem.schedule.location}</p>
                                  <p>👥 {classItem.enrollmentCount}/{classItem.maxEnrollment} students</p>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="schedule" className="space-y-6">
                  <Card>
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold mb-4 flex items-center">
                        <Clock className="h-5 w-5 mr-2" />
                        Office Hours & Schedule
                      </h3>
                      {faculty.officeHours && (
                        <p className="text-sm text-muted-foreground mb-4">
                          {faculty.officeHours}
                        </p>
                      )}
                      {faculty.office && (
                        <p className="text-sm text-muted-foreground mb-4">
                          <strong>Office:</strong> {faculty.office}
                        </p>
                      )}

                      {faculty.scheduledOfficeHours && faculty.scheduledOfficeHours.length > 0 && (
                        <div className="mt-6">
                          <h4 className="font-medium mb-3">Office Hours Schedule</h4>
                          <div className="space-y-4">
                            {faculty.scheduledOfficeHours.map((officeHour) => (
                              <div key={officeHour.id} className="border rounded-lg p-4">
                                <h5 className="font-medium text-sm mb-2">{officeHour.day}</h5>
                                <p className="text-xs text-muted-foreground mb-2">📍 {officeHour.location}</p>
                                <div className="space-y-1">
                                  {officeHour.timeSlots.map((slot) => (
                                    <div key={slot.id} className="flex justify-between items-center text-xs">
                                      <span>{slot.startTime} - {slot.endTime}</span>
                                      <span className={`px-2 py-1 rounded ${
                                        slot.isAvailable ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                                      }`}>
                                        {slot.isAvailable ? 'Available' : 'Booked'}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="opportunities" className="space-y-6">
                  <Card>
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold mb-4 flex items-center">
                        <UserPlus className="h-5 w-5 mr-2" />
                        Research Opportunities
                      </h3>
                      {faculty.researchProjects && faculty.researchProjects.length > 0 ? (
                        <div className="space-y-4">
                          {faculty.researchProjects.map((project) => (
                            <div key={project.id} className="border rounded-lg p-4">
                              <h4 className="font-medium text-sm mb-2">{project.title}</h4>
                              <p className="text-sm text-muted-foreground mb-4">{project.description}</p>

                              {project.requirements && project.requirements.length > 0 && (
                                <div className="mb-4">
                                  <h5 className="text-xs font-medium mb-2">Requirements:</h5>
                                  <ul className="list-disc list-inside text-xs text-muted-foreground">
                                    {project.requirements.map((req, index) => (
                                      <li key={index}>{req}</li>
                                    ))}
                                  </ul>
                                </div>
                              )}

                              <div className="flex flex-wrap gap-2 mb-4">
                                {project.tags.map((tag, index) => (
                                  <span key={index} className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded">
                                    {tag}
                                  </span>
                                ))}
                              </div>

                              <div className="text-xs text-muted-foreground space-y-1">
                                <p>⏱️ Timeline: {project.timeline}</p>
                                <p>👥 Positions: {project.positions}</p>
                                <p>⚡ Commitment: {project.commitment}</p>
                                <p>💰 {project.isPaid ? 'Paid position' : 'Unpaid position'}</p>
                                <p>📚 {project.isCredited ? 'Credit available' : 'No credit available'}</p>
                              </div>

                              <div className="mt-4 pt-4 border-t">
                                <Button size="sm" asChild>
                                  <a href={`mailto:${faculty.email}?subject=Interest in Research Project: ${project.title}`}>
                                    Contact about this opportunity
                                  </a>
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-muted-foreground">
                          No current research opportunities available. Please check back later or contact directly.
                        </p>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </PageTransition>
  );
}

// This function generates static params for all faculty members at build time
export async function generateStaticParams() {
  try {
    // Import prisma here to avoid circular dependencies
    const { prisma } = await import('@/lib/prisma')

    // Get all active faculty users from the database
    const facultyUsers = await prisma.user.findMany({
      where: {
        role: 'FACULTY',
        status: 'ACTIVE'
      },
      select: {
        id: true
      }
    })

    return facultyUsers.map((user) => ({
      id: user.id,
    }))
  } catch (error) {
    console.error('Error generating static params for faculty:', error)
    // Fallback to empty array if database is not available during build
    return []
  }
}