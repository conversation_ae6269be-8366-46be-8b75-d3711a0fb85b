import { prisma } from "@/lib/prisma"
import { FacultyDirectoryClient } from "@/components/faculty/FacultyDirectoryClient"

// Fetch faculty data from database
async function getFacultyData() {
  const [facultyProfiles, departments, researchAreas] = await Promise.all([
    prisma.facultyProfile.findMany({
      where: {
        user: {
          status: 'ACTIVE'
        }
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          image: true,
            status: true
          }
        },
        department: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        researchAreas: {
          select: {
            id: true,
            areaName: true
          }
        },
        publications: {
          take: 3,
          orderBy: {
            year: 'desc'
          },
          select: {
            title: true,
            year: true
          }
        }
      },
      orderBy: {
        user: {
          name: 'asc'
        }
      }
    }),
    prisma.department.findMany({
      select: {
        id: true,
        name: true,
        slug: true
      },
      orderBy: {
        name: 'asc'
      }
    }),
    prisma.facultyResearchArea.findMany({
      select: {
        id: true,
        areaName: true
      },
      orderBy: {
        areaName: 'asc'
      }
    })
  ])

  // Transform faculty data to match the expected format
  const facultyData = facultyProfiles.map(faculty => ({
    id: faculty.id,
    name: faculty.user.name || 'Unknown',
    title: faculty.title,
    department: faculty.department.name,
    departmentSlug: faculty.department.slug,
    email: faculty.user.email,
    phone: faculty.phone || '',
    officeLocation: faculty.officeLocation || '',
    websiteUrl: faculty.websiteUrl || '',
    bio: faculty.bio || '',
    imageUrl: faculty.user.image || `/images/faculty/default-avatar.svg`,
    altText: `${faculty.user.name || 'Faculty'} - ${faculty.title}`,
    researchAreas: faculty.researchAreas.map(ra => ra.areaName),
    publications: faculty.publications.map(pub => `${pub.title} (${pub.year})`),
    courses: [], // Could be populated from course assignments
    officeHours: faculty.officeHours || '',
    scholarId: faculty.scholarId || ''
  }))

  return {
    facultyData,
    departments,
    researchAreas: [...new Set(researchAreas.map(ra => ra.areaName))]
  }
}

export default async function FacultyDirectoryPage() {
  // Fetch data from database
  const { facultyData, departments, researchAreas } = await getFacultyData()

  return (
    <FacultyDirectoryClient
      facultyData={facultyData}
      departments={departments}
      researchAreas={researchAreas}
    />
  )
}