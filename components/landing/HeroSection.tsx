'use client'

import Link from "next/link"
import { ArrowRight } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { motion } from "framer-motion"

export default function HeroSection() {
  return (
    <section className="w-full py-16 md:py-24 lg:py-32 bg-gradient-to-b from-light to-background relative overflow-hidden">
      {/* Abstract geometric shapes inspired by logo */}
      <div className="absolute inset-0 overflow-hidden opacity-10">
        <motion.div
          className="absolute -top-20 -left-20 w-96 h-96 bg-crimson rounded-full blur-3xl"
          animate={{
            x: [0, 10, 0],
            y: [0, 15, 0]
          }}
          transition={{
            repeat: Infinity,
            duration: 8,
            ease: "easeInOut"
          }}
        ></motion.div>
        <motion.div
          className="absolute top-1/2 right-0 w-80 h-80 bg-gold rounded-full blur-3xl"
          animate={{
            x: [0, -15, 0],
            y: [0, 10, 0]
          }}
          transition={{
            repeat: Infinity,
            duration: 10,
            ease: "easeInOut",
            delay: 1
          }}
        ></motion.div>
        <motion.div
          className="absolute bottom-0 left-1/3 w-64 h-64 bg-crimson/70 rounded-full blur-3xl"
          animate={{
            x: [0, 20, 0],
            y: [0, -15, 0]
          }}
          transition={{
            repeat: Infinity,
            duration: 9,
            ease: "easeInOut",
            delay: 2
          }}
        ></motion.div>
      </div>

      <div className="px-4 md:px-6 relative">
        <div className="grid gap-8 lg:grid-cols-2 lg:gap-16 xl:grid-cols-2 items-center">
          <motion.div
            className="flex flex-col justify-center space-y-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              duration: 0.5,
              ease: "easeOut"
            }}
          >
            <div className="inline-flex items-center px-3 py-1 rounded-full bg-crimson/10 text-crimson text-sm font-medium mb-2 w-fit">
              Shaping Tomorrow's Leaders
            </div>
            <div className="space-y-4">
              <h1 className="heading-xl">
                Welcome to <span className="text-crimson">Ullens</span> College
              </h1>
              <p className="max-w-[600px] text-muted-foreground md:text-xl leading-relaxed">
                Discover our diverse schools across multiple disciplines, designed to prepare you for
                success in today's rapidly evolving world.
              </p>
            </div>
            <div className="flex flex-col gap-3 min-[400px]:flex-row pt-4">
              <Button className="shadow-md transition-all hover:shadow-lg">
                Explore Programs
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button variant="outline" className="border-crimson/20 hover:bg-crimson/5">
                Schedule a Visit
              </Button>
            </div>
          </motion.div>
          <motion.div
            className="flex items-center justify-center relative"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{
              duration: 0.5,
              delay: 0.2,
              ease: "easeOut"
            }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {/* Decorative element */}
            <div className="absolute -z-10 w-[120%] h-[120%] bg-gradient-to-tr from-crimson/10 via-gold/10 to-transparent rounded-full blur-2xl"></div>

            <motion.div
              className="relative w-full max-w-[550px] rounded-2xl overflow-hidden shadow-xl"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.3 }}
            >
              <img
                src="/images/campus/main-building.jpg"
                width={550}
                height={550}
                alt="College campus"
                className="aspect-square w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-crimson/20 to-transparent mix-blend-multiply"></div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}