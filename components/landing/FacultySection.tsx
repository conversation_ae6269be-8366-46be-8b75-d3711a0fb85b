import FacultyCard from "./FacultyCard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import { prisma } from "@/lib/prisma";

async function getFeaturedFaculty() {
  try {
    const faculty = await prisma.user.findMany({
      where: {
        role: 'FACULTY',
        status: 'ACTIVE'
      },
      include: {
        profile: true,
        facultyProfile: {
          include: {
            department: true
          }
        }
      },
      take: 4, // Show only 4 faculty on homepage
      orderBy: {
        createdAt: 'asc' // Show the first faculty members added
      }
    });

    return faculty.map(user => ({
      id: user.id,
      imageUrl: user.profile?.avatarUrl || "/placeholder.jpg",
      altText: user.name || "Faculty Member",
      name: user.name || "Faculty Member",
      title: user.facultyProfile?.title || "Faculty Member",
      bio: user.facultyProfile?.bio || "Dedicated educator and researcher.",
    }));
  } catch (error) {
    console.error('Error fetching featured faculty:', error);
    // Fallback to empty array if database fails
    return [];
  }
}

export default async function FacultySection() {
  const facultyData = await getFeaturedFaculty();

  // If no faculty data, show a placeholder message
  if (facultyData.length === 0) {
    return (
      <section id="faculty" className="w-full py-20 md:py-28 lg:py-32 bg-background relative overflow-hidden">
        <div className="px-4 md:px-6 relative">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="inline-flex items-center px-3 py-1 rounded-full bg-crimson/10 text-crimson text-sm font-medium">
              Our Faculty
            </div>
            <div className="space-y-4">
              <h2 className="heading-lg">
                Meet Our Distinguished Faculty
              </h2>
              <p className="text-muted-foreground text-lg leading-relaxed">
                Our faculty profiles are being updated. Please check back soon to meet our distinguished educators.
              </p>
            </div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section id="faculty" className="w-full py-20 md:py-28 lg:py-32 bg-background relative overflow-hidden">
      {/* Abstract geometric shapes */}
      <div className="absolute inset-0 overflow-hidden opacity-10">
        <div className="absolute top-1/3 left-1/4 w-72 h-72 bg-crimson rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-gold/70 rounded-full blur-3xl"></div>
      </div>

      <div className="px-4 md:px-6 relative">
        <div className="flex flex-col items-center justify-center space-y-4 text-center mb-16">
          <div className="inline-flex items-center px-3 py-1 rounded-full bg-crimson/10 text-crimson text-sm font-medium">
            Our Faculty
          </div>
          <div className="space-y-4">
            <h2 className="heading-lg">
              Meet Our Distinguished Faculty
            </h2>
            <p className="text-muted-foreground text-lg leading-relaxed">
              Our dedicated professors and staff bring years of industry experience and academic excellence to provide you with the best education.
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
          {facultyData.map((faculty) => (
            <FacultyCard
              key={faculty.id}
              id={faculty.id}
              imageUrl={faculty.imageUrl}
              altText={faculty.altText}
              name={faculty.name}
              title={faculty.title}
              bio={faculty.bio}
            />
          ))}
        </div>

        <div className="flex justify-center mt-12">
          <Link href="/faculty">
            <Button className="shadow-md transition-all hover:shadow-lg hover:scale-105 duration-300">
              Meet All Faculty
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}