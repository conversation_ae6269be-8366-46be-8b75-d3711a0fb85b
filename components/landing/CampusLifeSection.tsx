import GalleryItem from "./GalleryItem";
import FeatureCard from "./FeatureCard";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Users, MapPin, Image as ImageIcon } from "lucide-react"; // Assuming Image is imported as ImageIcon from page

const galleryData = [
  {
    imageUrl: "/images/campus/main-building.jpg",
    altText: "Campus Main Building",
    title: "Modern Campus Facilities",
    description: "Our state-of-the-art campus features modern classrooms, research labs, and collaborative spaces.",
    parallax: true,
  },
  {
    imageUrl: "/images/campus/student-activities.jpg",
    altText: "Student Activities",
    title: "Student Activities",
    description: "From clubs and sports to cultural events, our campus offers diverse extracurricular opportunities.",
    parallax: true,
  },
  {
    imageUrl: "/images/campus/research-facilities.jpg",
    altText: "Research Facilities",
    title: "Research Facilities",
    description: "Our cutting-edge research facilities support innovation and academic excellence.",
    parallax: true,
  },
];

const featureData = [
  {
    icon: <Users className="h-6 w-6 text-crimson" />,
    title: "Student Community",
    description: "Join a diverse and inclusive community of students from around the world.",
    themeColor: 'crimson',
  },
  {
    icon: <MapPin className="h-6 w-6 text-gold" />,
    title: "Prime Location",
    description: "Our campus is located in a beautiful setting with easy access to urban amenities.",
    themeColor: 'gold',
  },
  {
    icon: <ImageIcon className="h-6 w-6 text-crimson" />,
    title: "Modern Facilities",
    description: "Enjoy state-of-the-art classrooms, labs, libraries, and recreational facilities.",
    themeColor: 'crimson',
  },
];

export default function CampusLifeSection() {
  // Parallax effect is initialized in the main page (CollegePage)
  // or could be initialized here if this component is always visible and needs it.

  return (
    <section id="campus-life" className="w-full py-20 md:py-28 lg:py-32 bg-light relative overflow-hidden">
      <div className="px-4 md:px-6 relative">
        <div className="flex flex-col items-center justify-center space-y-4 text-center mb-16">
          <div className="inline-flex items-center px-3 py-1 rounded-full bg-gold/10 text-gold text-sm font-medium">
            Campus Life
          </div>
          <div className="space-y-4">
            <h2 className="heading-lg">
              Experience Life at Ullens College
            </h2>
            <p className="text-muted-foreground text-lg leading-relaxed">
              Our vibrant campus offers a rich student experience with modern facilities, diverse activities, and a supportive community.
            </p>
          </div>
        </div>
      </div>

      {/* Full-width image gallery with parallax effect */}
      <div className="relative w-full overflow-hidden">
        <div className="flex flex-nowrap overflow-x-auto snap-x snap-mandatory scrollbar-hide">
          {galleryData.map((item) => (
            <GalleryItem
              key={item.title}
              imageUrl={item.imageUrl}
              altText={item.altText}
              title={item.title}
              description={item.description}
              parallax={item.parallax}
            />
          ))}
        </div>
        {/* Scroll indicator for gallery */}
        <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2">
          <div className="h-2 w-2 rounded-full bg-white"></div>
          <div className="h-2 w-2 rounded-full bg-white/50"></div>
          <div className="h-2 w-2 rounded-full bg-white/50"></div>
        </div>
      </div>

      <div className="px-4 md:px-6 mt-16">
        <div className="">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {featureData.map((feature) => (
              <FeatureCard
                key={feature.title}
                icon={feature.icon}
                title={feature.title}
                description={feature.description}
                themeColor={feature.themeColor as 'crimson' | 'gold'}
              />
            ))}
          </div>

          <div className="flex justify-center mt-12">
            <Button className="shadow-md transition-all hover:shadow-lg hover:scale-105 duration-300">
              Schedule a Campus Tour
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}