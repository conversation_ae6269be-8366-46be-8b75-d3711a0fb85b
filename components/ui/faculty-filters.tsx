'use client'

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Search, X, Filter, Grid, Network, Grip, Sparkles } from "lucide-react"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"

export type FacultyFilterProps = {
  departments: string[]
  researchAreas: string[]
  onFilterChange: (filters: {
    search: string
    department: string
    researchArea: string
    expertiseFilter: string[]
    viewMode: 'grid' | 'org' | 'research'
  }) => void
  activeViewMode: 'grid' | 'org' | 'research'
  onViewModeChange: (mode: 'grid' | 'org' | 'research') => void
  searchSuggestions?: string[]
  expertiseOptions?: string[]
}

export function FacultyFilters({
  departments,
  researchAreas,
  onFilterChange,
  activeViewMode,
  onViewModeChange,
  searchSuggestions = [],
  expertiseOptions = ["AI & Machine Learning", "Cybersecurity", "Education", "Business", "Climate Science"]
}: FacultyFilterProps) {
  const [search, setSearch] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [department, setDepartment] = useState('all')
  const [researchArea, setResearchArea] = useState('all')
  const [expertiseFilter, setExpertiseFilter] = useState<string[]>([])
  const [filtersOpen, setFiltersOpen] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const searchInputRef = useRef<HTMLInputElement>(null)

  // Handle search input change with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearch(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Apply filters when any filter changes
  useEffect(() => {
    onFilterChange({
      search,
      department: department === 'all' ? '' : department,
      researchArea: researchArea === 'all' ? '' : researchArea,
      expertiseFilter,
      viewMode: activeViewMode
    })
  }, [search, department, researchArea, expertiseFilter, activeViewMode])

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery('')
    setSearch('')
    setDepartment('all')
    setResearchArea('all')
    setExpertiseFilter([])
  }

  // Check if any filters are active
  const isFiltersActive = search || department !== 'all' || researchArea !== 'all' || expertiseFilter.length > 0

  // Handle expertise filter change
  const handleExpertiseChange = (value: string) => {
    setExpertiseFilter(prev => {
      if (prev.includes(value)) {
        return prev.filter(item => item !== value);
      } else {
        return [...prev, value];
      }
    });
  };

  // Handle search suggestion selection
  const handleSuggestionSelect = (suggestion: string) => {
    setSearchQuery(suggestion);
    setShowSuggestions(false);
    searchInputRef.current?.focus();
  };

  // Handle keyboard navigation for search
  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Escape') {
      setShowSuggestions(false);
    } else if (e.key === 'ArrowDown' && showSuggestions) {
      e.preventDefault();
      const suggestionElements = document.querySelectorAll('[data-suggestion]');
      if (suggestionElements.length > 0) {
        (suggestionElements[0] as HTMLElement).focus();
      }
    }
  };

  return (
    <div className="w-full space-y-4" aria-label="Faculty search filters">
      <div className="flex flex-col lg:flex-row gap-4">
        {/* Search Bar */}
        <div className="relative w-full lg:flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            ref={searchInputRef}
            placeholder="Search by name, expertise, research area..."
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setShowSuggestions(e.target.value.length > 0);
            }}
            onFocus={() => setShowSuggestions(searchQuery.length > 0)}
            onBlur={() => {
              // Delay hiding suggestions to allow for clicks
              setTimeout(() => setShowSuggestions(false), 200);
            }}
            onKeyDown={handleSearchKeyDown}
            className="pl-10 pr-10"
            aria-label="Search faculty"
            aria-autocomplete="list"
            aria-controls={showSuggestions ? "search-suggestions" : undefined}
            aria-expanded={showSuggestions}
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-1 top-1/2 h-7 w-7 -translate-y-1/2 text-muted-foreground hover:text-foreground"
              onClick={() => {
                setSearchQuery('');
                setSearch('');
                searchInputRef.current?.focus();
              }}
              aria-label="Clear search"
            >
              <X className="h-4 w-4" />
            </Button>
          )}

          {/* Search Suggestions Dropdown */}
          {showSuggestions && searchSuggestions.length > 0 && (
            <div
              id="search-suggestions"
              className="absolute top-full left-0 w-full z-50 mt-1 bg-background border rounded-md shadow-md max-h-[200px] overflow-auto"
              role="listbox"
            >
              <ScrollArea className="max-h-[200px]">
                <div className="p-1">
                  {searchSuggestions
                    .filter(suggestion =>
                      suggestion.toLowerCase().includes(searchQuery.toLowerCase())
                    )
                    .slice(0, 5)
                    .map((suggestion, index) => (
                      <button
                        key={index}
                        className="w-full text-left px-3 py-2 hover:bg-muted rounded-sm transition-colors"
                        onClick={() => handleSuggestionSelect(suggestion)}
                        onKeyDown={(e) => {
                          if (e.key === 'ArrowDown') {
                            e.preventDefault();
                            const nextElement = document.querySelectorAll('[data-suggestion]')[index + 1] as HTMLElement;
                            if (nextElement) nextElement.focus();
                          } else if (e.key === 'ArrowUp') {
                            e.preventDefault();
                            if (index === 0) {
                              searchInputRef.current?.focus();
                            } else {
                              const prevElement = document.querySelectorAll('[data-suggestion]')[index - 1] as HTMLElement;
                              if (prevElement) prevElement.focus();
                            }
                          } else if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            handleSuggestionSelect(suggestion);
                          } else if (e.key === 'Escape') {
                            setShowSuggestions(false);
                            searchInputRef.current?.focus();
                          }
                        }}
                        data-suggestion
                        role="option"
                        aria-selected={false}
                        tabIndex={-1}
                      >
                        <div className="flex items-center">
                          <Search className="h-4 w-4 mr-2 text-muted-foreground" />
                          <span>{suggestion}</span>
                        </div>
                      </button>
                    ))}
                  {searchSuggestions.filter(s =>
                    s.toLowerCase().includes(searchQuery.toLowerCase())
                  ).length === 0 && (
                    <div className="px-3 py-2 text-muted-foreground">
                      No suggestions found
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>
          )}
        </div>

        {/* Advanced Filters Button */}
        <Popover open={filtersOpen} onOpenChange={setFiltersOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="lg:w-auto"
              aria-label={`${filtersOpen ? 'Hide' : 'Show'} advanced filters`}
            >
              <Filter className="mr-2 h-4 w-4" />
              <span>Filters</span>
              {(department !== 'all' || researchArea !== 'all' || expertiseFilter.length > 0) && (
                <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 flex items-center justify-center">
                  {(department !== 'all' ? 1 : 0) +
                   (researchArea !== 'all' ? 1 : 0) +
                   (expertiseFilter.length)}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[280px] p-4" align="end">
            <div className="space-y-4">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Department</h3>
                <Select value={department} onValueChange={setDepartment}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="All Departments" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Departments</SelectItem>
                    {departments.map((dept) => (
                      <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Research Area</h3>
                <Select value={researchArea} onValueChange={setResearchArea}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="All Research Areas" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Research Areas</SelectItem>
                    {researchAreas.map((area) => (
                      <SelectItem key={area} value={area}>{area}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Expertise</h3>
                <div className="border rounded-md p-3 space-y-2 max-h-[150px] overflow-y-auto">
                  {expertiseOptions.map((option) => (
                    <div key={option} className="flex items-center space-x-2">
                      <Checkbox
                        id={`expertise-${option}`}
                        checked={expertiseFilter.includes(option)}
                        onCheckedChange={() => handleExpertiseChange(option)}
                      />
                      <label
                        htmlFor={`expertise-${option}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {option}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => {
                  setDepartment('all');
                  setResearchArea('all');
                  setExpertiseFilter([]);
                  setFiltersOpen(false);
                }}
              >
                Reset Filters
              </Button>
            </div>
          </PopoverContent>
        </Popover>

        {/* View Mode Toggle */}
        <div className="flex border rounded-md overflow-hidden">
          <Button
            variant={activeViewMode === 'grid' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => onViewModeChange('grid')}
            className="rounded-none flex-1 h-10"
            aria-label="Grid view"
            aria-pressed={activeViewMode === 'grid'}
          >
            <Grid className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Grid</span>
          </Button>
          <Button
            variant={activeViewMode === 'org' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => onViewModeChange('org')}
            className="rounded-none flex-1 h-10"
            aria-label="Org chart view"
            aria-pressed={activeViewMode === 'org'}
          >
            <Grip className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Org Chart</span>
          </Button>
          <Button
            variant={activeViewMode === 'research' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => onViewModeChange('research')}
            className="rounded-none flex-1 h-10"
            aria-label="Research network view"
            aria-pressed={activeViewMode === 'research'}
          >
            <Network className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Research</span>
          </Button>
        </div>
      </div>

      {/* Active Filters Display */}
      {isFiltersActive && (
        <div className="flex flex-wrap items-center gap-2 p-3 bg-muted/30 rounded-md" role="region" aria-label="Active filters">
          <span className="text-sm font-medium text-muted-foreground mr-1">Active filters:</span>

          {search && (
            <Badge variant="secondary" className="px-2 py-1 flex items-center gap-1">
              <span>Search: {search}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 ml-1 text-muted-foreground hover:text-foreground"
                onClick={() => { setSearchQuery(''); setSearch(''); }}
                aria-label={`Remove search filter: ${search}`}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {department !== 'all' && (
            <Badge variant="secondary" className="px-2 py-1 flex items-center gap-1">
              <span>Department: {department}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 ml-1 text-muted-foreground hover:text-foreground"
                onClick={() => setDepartment('all')}
                aria-label={`Remove department filter: ${department}`}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {researchArea !== 'all' && (
            <Badge variant="secondary" className="px-2 py-1 flex items-center gap-1">
              <span>Research: {researchArea}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 ml-1 text-muted-foreground hover:text-foreground"
                onClick={() => setResearchArea('all')}
                aria-label={`Remove research area filter: ${researchArea}`}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {expertiseFilter.map((expertise) => (
            <Badge key={expertise} variant="secondary" className="px-2 py-1 flex items-center gap-1">
              <span>Expertise: {expertise}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 ml-1 text-muted-foreground hover:text-foreground"
                onClick={() => handleExpertiseChange(expertise)}
                aria-label={`Remove expertise filter: ${expertise}`}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}

          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="ml-auto text-xs h-7"
            aria-label="Clear all filters"
          >
            Clear All
          </Button>
        </div>
      )}
    </div>
  )
}