'use client'

import React, { useState, useEffect, useRef } from 'react'
import { ImageSkeleton } from './loading-spinner'

interface LazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string
  alt: string
  aspectRatio?: string
  className?: string
  loadingColor?: string
  fallbackSrc?: string
}

export function LazyImage({
  src,
  alt,
  aspectRatio = 'aspect-video',
  className = '',
  loadingColor = 'bg-muted/50',
  fallbackSrc = '/images/faculty/default-avatar.svg',
  ...props
}: LazyImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isInView, setIsInView] = useState(false)
  const [currentSrc, setCurrentSrc] = useState(src)
  const [hasError, setHasError] = useState(false)
  const imgRef = useRef<HTMLImageElement>(null)

  // Reset state when src changes
  useEffect(() => {
    setCurrentSrc(src)
    setHasError(false)
    setIsLoaded(false)
  }, [src])

  useEffect(() => {
    // Skip if image is already loaded or not yet in view
    if (isLoaded || !isInView) return

    const img = imgRef.current
    if (!img) return

    const onLoad = () => {
      setIsLoaded(true)
    }

    const onError = () => {
      console.error(`Failed to load image: ${currentSrc}`)
      if (!hasError && fallbackSrc && currentSrc !== fallbackSrc) {
        setHasError(true)
        setCurrentSrc(fallbackSrc)
        setIsLoaded(false) // Reset to try loading fallback
      } else {
        setIsLoaded(true) // Still mark as "loaded" to remove skeleton
      }
    }

    // Add event listeners
    img.addEventListener('load', onLoad)
    img.addEventListener('error', onError)

    // Clean up
    return () => {
      img.removeEventListener('load', onLoad)
      img.removeEventListener('error', onError)
    }
  }, [currentSrc, isLoaded, isInView, hasError, fallbackSrc])

  useEffect(() => {
    // Set up intersection observer to detect when image is in viewport
    if (!imgRef.current) return

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      {
        rootMargin: '200px', // Start loading when image is 200px from viewport
        threshold: 0.01,
      }
    )

    observer.observe(imgRef.current)

    return () => {
      observer.disconnect()
    }
  }, [])

  return (
    <div className={`relative overflow-hidden ${aspectRatio} ${className}`}>
      {!isLoaded && <ImageSkeleton aspectRatio="" className="absolute inset-0" />}

      <img
        ref={imgRef}
        src={isInView ? currentSrc : undefined}
        alt={alt}
        className={`w-full h-full object-cover transition-opacity duration-300 ${
          isLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        loading="lazy"
        {...props}
      />
    </div>
  )
}
