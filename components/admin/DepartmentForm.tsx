"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Save, ArrowLeft } from "lucide-react"
import Link from "next/link"

const departmentSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  slug: z.string().min(1, 'Slug is required').regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
  description: z.string().optional(),
  headFacultyId: z.string().optional(),
})

type DepartmentFormData = z.infer<typeof departmentSchema>

interface Faculty {
  id: string
  title: string
  user: {
    name: string | null
  }
}

interface DepartmentFormProps {
  departmentId?: string
  initialData?: Partial<DepartmentFormData>
  faculty: Faculty[]
}

export function DepartmentForm({ departmentId, initialData, faculty }: DepartmentFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()
  const router = useRouter()
  const isEditing = !!departmentId

  const form = useForm<DepartmentFormData>({
    resolver: zodResolver(departmentSchema),
    defaultValues: {
      name: initialData?.name || '',
      slug: initialData?.slug || '',
      description: initialData?.description || '',
      headFacultyId: initialData?.headFacultyId || '',
    }
  })

  // Auto-generate slug from name
  const handleNameChange = (name: string) => {
    form.setValue('name', name)
    if (!isEditing) {
      const slug = name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()
      form.setValue('slug', slug)
    }
  }

  const onSubmit = async (data: DepartmentFormData) => {
    setIsLoading(true)
    
    try {
      const url = isEditing ? `/api/admin/departments/${departmentId}` : '/api/admin/departments'
      const method = isEditing ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || `Failed to ${isEditing ? 'update' : 'create'} department`)
      }

      toast({
        title: "Success",
        description: `Department ${isEditing ? 'updated' : 'created'} successfully.`,
      })

      router.push('/admin/departments')
      router.refresh()
    } catch (error) {
      console.error('Error saving department:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An error occurred. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {isEditing ? 'Edit Department' : 'Add Department'}
          </h1>
          <p className="text-gray-600">
            {isEditing ? 'Update department information' : 'Create a new academic department'}
          </p>
        </div>
        <Button variant="outline" asChild>
          <Link href="/admin/departments">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Departments
          </Link>
        </Button>
      </div>

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Department Information</CardTitle>
            <CardDescription>Basic details about the department</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Department Name *</Label>
              <Input
                id="name"
                value={form.watch('name')}
                onChange={(e) => handleNameChange(e.target.value)}
                placeholder="Computer Science"
              />
              {form.formState.errors.name && (
                <p className="text-sm text-red-600">{form.formState.errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="slug">URL Slug *</Label>
              <Input
                id="slug"
                {...form.register('slug')}
                placeholder="computer-science"
                disabled={isEditing}
              />
              {form.formState.errors.slug && (
                <p className="text-sm text-red-600">{form.formState.errors.slug.message}</p>
              )}
              <p className="text-sm text-gray-500">
                Used in URLs. {isEditing ? 'Cannot be changed after creation.' : 'Auto-generated from name.'}
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...form.register('description')}
                placeholder="Brief description of the department..."
                rows={4}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="headFacultyId">Department Head</Label>
              <Select
                value={form.watch('headFacultyId')}
                onValueChange={(value) => form.setValue('headFacultyId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select department head (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No department head</SelectItem>
                  {faculty.map((member) => (
                    <SelectItem key={member.id} value={member.id}>
                      {member.user.name} - {member.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
            <Save className="w-4 h-4 mr-2" />
            {isEditing ? 'Update Department' : 'Create Department'}
          </Button>
        </div>
      </form>
    </div>
  )
}
