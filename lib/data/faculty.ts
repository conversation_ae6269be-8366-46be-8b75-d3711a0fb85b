// Define types for faculty data
export interface Publication {
  id: string
  title: string
  authors: string[]
  journal: string
  year: number
  citationCount: number
  link: string
  abstract?: string
  tags?: string[]
}

export interface TimelineEvent {
  id: string
  year: string
  title: string
  description: string
  type: 'education' | 'position' | 'award'
}

export interface CVDocument {
  id: string
  title: string
  fileType: 'pdf' | 'docx' | 'txt'
  fileSize: string
  lastUpdated: string
  url: string
}

export interface CourseClass {
  id: string
  courseCode: string
  courseName: string
  semester: string
  schedule: {
    days: string[]
    time: string
    location: string
  }
  enrollmentCount: number
  maxEnrollment: number
  syllabus?: string
  description?: string
  status: 'upcoming' | 'current' | 'past'
}

export interface TimeSlot {
  id: string
  startTime: string
  endTime: string
  isAvailable: boolean
  isBooked?: boolean
  studentName?: string
  studentEmail?: string
  topic?: string
}

export interface OfficeHour {
  id: string
  day: string
  timeSlots: TimeSlot[]
  location: string
  notes?: string
}

export interface ResearchProject {
  id: string
  title: string
  description: string
  requirements?: string[]
  timeline: string
  positions: number
  commitment: string
  isPaid: boolean
  isCredited: boolean
  tags: string[]
  status: 'recruiting' | 'ongoing' | 'completed'
  imageUrl?: string
}

export interface FacultyMember {
  id: string
  imageUrl: string
  altText: string
  name: string
  title: string
  department: string
  email: string
  website: string
  bio: string
  education: string[]
  research: string[]
  publications: string[]
  courses?: string[]
  officeHours?: string
  office?: string
  // Extended data
  scholarId?: string
  timeline?: TimelineEvent[]
  cvDocuments?: CVDocument[]
  scholarlyPublications?: Publication[]
  upcomingClasses?: CourseClass[]
  scheduledOfficeHours?: OfficeHour[]
  researchProjects?: ResearchProject[]
}

export interface ResearchArea {
  name: string
  facultyIds: string[]
  color: string
}

// Expanded faculty data with unique IDs and more details
export const facultyData: FacultyMember[] = [
  {
    id: "sarah-johnson",
    imageUrl: "/placeholder.jpg",
    altText: "Dr. Sarah Johnson",
    name: "Dr. Sarah Johnson",
    title: "Dean, Computer Science",
    department: "Computer Science",
    email: "<EMAIL>",
    website: "https://faculty.college.edu/sjohnson",
    bio: "Ph.D. in Computer Science from MIT with 15+ years of experience in AI research and education.",
    education: [
      "Ph.D. Computer Science, Massachusetts Institute of Technology",
      "M.S. Computer Science, Stanford University",
      "B.S. Computer Engineering, University of California, Berkeley"
    ],
    research: [
      "Artificial Intelligence and Machine Learning",
      "Neural Networks and Deep Learning",
      "Computer Vision and Pattern Recognition"
    ],
    publications: [
      "Johnson, S. et al. (2022). 'Advanced Neural Networks for Image Recognition'. Journal of AI Research.",
      "Johnson, S. & Smith, T. (2020). 'Applications of Deep Learning in Healthcare'. AI in Medicine Journal.",
      "Johnson, S. (2018). 'Future Directions in Machine Learning'. Computational Intelligence Review."
    ],
    courses: [
      "CS-4010: Advanced Artificial Intelligence",
      "CS-3050: Machine Learning Fundamentals",
      "CS-2020: Data Structures and Algorithms"
    ],
    officeHours: "Mondays and Wednesdays: 2:00 PM - 4:00 PM",
    office: "Computer Science Building, Room 305",
    // Extended data
    scholarId: "abcd1234",
    timeline: [
      {
        id: "timeline-1",
        year: "2018-Present",
        title: "Dean of Computer Science",
        description: "Leading the department's strategic initiatives and academic programs at University College.",
        type: "position"
      },
      {
        id: "timeline-2",
        year: "2010-2018",
        title: "Associate Professor",
        description: "Taught advanced courses in AI and Machine Learning while conducting research in neural networks.",
        type: "position"
      },
      {
        id: "timeline-3",
        year: "2005-2010",
        title: "Assistant Professor",
        description: "Started tenure-track position focusing on computer vision research.",
        type: "position"
      },
      {
        id: "timeline-4",
        year: "2005",
        title: "Ph.D. Computer Science",
        description: "Dissertation on 'Neural Network Architectures for Visual Recognition Tasks'",
        type: "education"
      },
      {
        id: "timeline-5",
        year: "2016",
        title: "Outstanding Research Award",
        description: "Recognized for contributions to deep learning research by the AI Society",
        type: "award"
      }
    ],
    cvDocuments: [
      {
        id: "cv-1",
        title: "Academic CV",
        fileType: "pdf",
        fileSize: "1.2 MB",
        lastUpdated: "January 15, 2023",
        url: "#"
      },
      {
        id: "cv-2",
        title: "Research Statement",
        fileType: "pdf",
        fileSize: "450 KB",
        lastUpdated: "March 3, 2023",
        url: "#"
      }
    ],
    scholarlyPublications: [
      {
        id: "pub-1",
        title: "Advanced Neural Networks for Image Recognition",
        authors: ["Sarah Johnson", "Thomas Smith", "Maya Wong"],
        journal: "Journal of AI Research",
        year: 2022,
        citationCount: 45,
        link: "#",
        abstract: "This paper presents a novel neural network architecture that improves image recognition accuracy by 15% compared to previous state-of-the-art methods."
      },
      {
        id: "pub-2",
        title: "Applications of Deep Learning in Healthcare",
        authors: ["Sarah Johnson", "Thomas Smith"],
        journal: "AI in Medicine Journal",
        year: 2020,
        citationCount: 78,
        link: "#",
        abstract: "A comprehensive review of deep learning applications in healthcare, focusing on diagnostic imaging, patient monitoring, and treatment planning."
      },
      {
        id: "pub-3",
        title: "Future Directions in Machine Learning",
        authors: ["Sarah Johnson"],
        journal: "Computational Intelligence Review",
        year: 2018,
        citationCount: 120,
        link: "#",
        abstract: "This paper explores emerging trends in machine learning research and identifies promising directions for future investigation."
      }
    ],
    upcomingClasses: [
      {
        id: "class-1",
        courseCode: "CS-4010",
        courseName: "Advanced Artificial Intelligence",
        semester: "Fall 2023",
        schedule: {
          days: ["M", "W"],
          time: "14:00-15:30",
          location: "CS Building, Room 305"
        },
        enrollmentCount: 28,
        maxEnrollment: 30,
        syllabus: "#",
        description: "Advanced topics in AI including deep learning, reinforcement learning, and natural language processing.",
        status: "current"
      },
      {
        id: "class-2",
        courseCode: "CS-3050",
        courseName: "Machine Learning Fundamentals",
        semester: "Fall 2023",
        schedule: {
          days: ["T", "Th"],
          time: "10:00-11:30",
          location: "CS Building, Room 210"
        },
        enrollmentCount: 35,
        maxEnrollment: 40,
        syllabus: "#",
        description: "Introduction to core machine learning algorithms and techniques.",
        status: "current"
      },
      {
        id: "class-3",
        courseCode: "CS-5100",
        courseName: "Deep Learning Seminar",
        semester: "Spring 2024",
        schedule: {
          days: ["W"],
          time: "15:00-18:00",
          location: "CS Building, Room 305"
        },
        enrollmentCount: 10,
        maxEnrollment: 15,
        status: "upcoming"
      }
    ],
    scheduledOfficeHours: [
      {
        id: "office-1",
        day: "Monday",
        timeSlots: [
          {
            id: "slot-1",
            startTime: "14:00",
            endTime: "14:30",
            isAvailable: true
          },
          {
            id: "slot-2",
            startTime: "14:30",
            endTime: "15:00",
            isAvailable: true,
            isBooked: true
          },
          {
            id: "slot-3",
            startTime: "15:00",
            endTime: "15:30",
            isAvailable: true
          },
          {
            id: "slot-4",
            startTime: "15:30",
            endTime: "16:00",
            isAvailable: true
          }
        ],
        location: "CS Building, Room 305"
      },
      {
        id: "office-2",
        day: "Wednesday",
        timeSlots: [
          {
            id: "slot-5",
            startTime: "14:00",
            endTime: "14:30",
            isAvailable: true
          },
          {
            id: "slot-6",
            startTime: "14:30",
            endTime: "15:00",
            isAvailable: true
          },
          {
            id: "slot-7",
            startTime: "15:00",
            endTime: "15:30",
            isAvailable: true
          },
          {
            id: "slot-8",
            startTime: "15:30",
            endTime: "16:00",
            isAvailable: true
          }
        ],
        location: "CS Building, Room 305",
        notes: "Virtual appointments available upon request."
      }
    ],
    researchProjects: [
      {
        id: "project-1",
        title: "Neural Networks for Medical Image Analysis",
        description: "Developing advanced neural networks for accurate diagnosis of medical conditions from imaging data.",
        requirements: [
          "Strong programming skills in Python",
          "Experience with deep learning frameworks (PyTorch or TensorFlow)",
          "Interest in healthcare applications"
        ],
        timeline: "Fall 2023 - Spring 2024",
        positions: 2,
        commitment: "10 hours/week",
        isPaid: true,
        isCredited: true,
        tags: ["Deep Learning", "Computer Vision", "Healthcare", "Neural Networks"],
        status: "recruiting"
      },
      {
        id: "project-2",
        title: "Reinforcement Learning for Robotics",
        description: "Exploring reinforcement learning techniques to improve robot navigation and decision-making.",
        requirements: [
          "Coursework in AI/ML",
          "Programming experience",
          "Interest in robotics"
        ],
        timeline: "Fall 2023 - Summer 2024",
        positions: 1,
        commitment: "8-12 hours/week",
        isPaid: false,
        isCredited: true,
        tags: ["Reinforcement Learning", "Robotics", "AI"],
        status: "recruiting"
      },
      {
        id: "project-3",
        title: "Natural Language Understanding",
        description: "Researching improved methods for natural language understanding in conversational AI systems.",
        timeline: "Fall 2022 - Spring 2023",
        positions: 3,
        commitment: "10 hours/week",
        isPaid: true,
        isCredited: false,
        tags: ["NLP", "Conversational AI", "Machine Learning"],
        status: "ongoing",
        imageUrl: "/placeholder.jpg"
      }
    ]
  },
  {
    id: "michael-chen",
    imageUrl: "/placeholder.jpg",
    altText: "Prof. Michael Chen",
    name: "Prof. Michael Chen",
    title: "Chair, Business School",
    department: "Business",
    email: "<EMAIL>",
    website: "https://faculty.college.edu/mchen",
    bio: "MBA from Harvard Business School with extensive experience in corporate leadership and entrepreneurship.",
    education: [
      "MBA, Harvard Business School",
      "B.S. Economics, University of Pennsylvania"
    ],
    research: [
      "Strategic Management",
      "Entrepreneurship and Innovation",
      "Corporate Finance"
    ],
    publications: [
      "Chen, M. (2021). 'Strategic Innovation in Emerging Markets'. Journal of Business Strategy.",
      "Chen, M. & Williams, J. (2019). 'Entrepreneurial Ecosystems in Digital Age'. Business Innovation Review.",
      "Chen, M. (2017). 'Corporate Leadership in Times of Change'. Leadership Quarterly."
    ],
    courses: [
      "BUS-4100: Strategic Management",
      "BUS-3200: Entrepreneurship",
      "BUS-2100: Principles of Management"
    ],
    officeHours: "Tuesdays and Thursdays: 1:00 PM - 3:00 PM",
    office: "Business Building, Room 210"
  },
  {
    id: "emily-rodriguez",
    imageUrl: "/placeholder.jpg",
    altText: "Dr. Emily Rodriguez",
    name: "Dr. Emily Rodriguez",
    title: "Professor, Agriculture",
    department: "Agriculture and Climate Science",
    email: "<EMAIL>",
    website: "https://faculty.college.edu/erodriguez",
    bio: "Ph.D. in Environmental Science with research focus on sustainable farming practices and climate adaptation.",
    education: [
      "Ph.D. Environmental Science, University of California, Davis",
      "M.S. Agricultural Science, Cornell University",
      "B.S. Environmental Studies, University of Washington"
    ],
    research: [
      "Sustainable Agriculture",
      "Climate Adaptation for Farming",
      "Agroecology and Biodiversity"
    ],
    publications: [
      "Rodriguez, E. et al. (2022). 'Climate-Resilient Agricultural Practices'. Journal of Sustainable Agriculture.",
      "Rodriguez, E. & Kumar, A. (2020). 'Biodiversity in Sustainable Farming'. Agroecology Journal.",
      "Rodriguez, E. (2018). 'Water Conservation Techniques in Agriculture'. Environmental Management Review."
    ],
    courses: [
      "AGR-4010: Sustainable Farming Systems",
      "AGR-3050: Climate Adaptation in Agriculture",
      "AGR-2020: Introduction to Agroecology"
    ],
    officeHours: "Wednesdays and Fridays: 10:00 AM - 12:00 PM",
    office: "Agriculture Building, Room 120"
  },
  {
    id: "james-wilson",
    imageUrl: "/placeholder.jpg",
    altText: "Dr. James Wilson",
    name: "Dr. James Wilson",
    title: "Director, Education",
    department: "Education",
    email: "<EMAIL>",
    website: "https://faculty.college.edu/jwilson",
    bio: "Ed.D. in Educational Leadership with 20+ years of experience in curriculum development and teacher training.",
    education: [
      "Ed.D. Educational Leadership, Columbia University",
      "M.Ed. Curriculum and Instruction, University of Michigan",
      "B.A. Education, Ohio State University"
    ],
    research: [
      "Educational Technology",
      "Teacher Professional Development",
      "Inclusive Education Practices"
    ],
    publications: [
      "Wilson, J. (2022). 'Digital Transformation in Education'. Educational Technology Journal.",
      "Wilson, J. & Martinez, L. (2020). 'Inclusive Teaching Strategies'. Journal of Teacher Education.",
      "Wilson, J. (2018). 'Professional Development Models for Educators'. Educational Leadership Review."
    ],
    courses: [
      "EDU-4100: Educational Leadership",
      "EDU-3200: Technology in Education",
      "EDU-2100: Curriculum Design"
    ],
    officeHours: "Mondays and Thursdays: 11:00 AM - 1:00 PM",
    office: "Education Building, Room 405"
  },
  {
    id: "amira-patel",
    imageUrl: "/placeholder.jpg",
    altText: "Dr. Amira Patel",
    name: "Dr. Amira Patel",
    title: "Associate Professor, Computer Science",
    department: "Computer Science",
    email: "<EMAIL>",
    website: "https://faculty.college.edu/apatel",
    bio: "Ph.D. in Computer Science specializing in cybersecurity and network systems, with industry experience at major tech companies.",
    education: [
      "Ph.D. Computer Science, Carnegie Mellon University",
      "M.S. Information Security, Georgia Tech",
      "B.S. Computer Science, University of Illinois"
    ],
    research: [
      "Cybersecurity",
      "Network Security",
      "Privacy-Preserving Computing"
    ],
    publications: [
      "Patel, A. et al. (2021). 'Advanced Threat Detection Systems'. Journal of Cybersecurity.",
      "Patel, A. & Garcia, N. (2019). 'Privacy in Distributed Systems'. Network Security Journal.",
      "Patel, A. (2017). 'Encryption Methods for Cloud Storage'. Cloud Computing Review."
    ],
    courses: [
      "CS-4200: Advanced Network Security",
      "CS-3300: Cryptography",
      "CS-2300: Introduction to Cybersecurity"
    ],
    officeHours: "Tuesdays and Fridays: 3:00 PM - 5:00 PM",
    office: "Computer Science Building, Room 210"
  },
  {
    id: "david-kim",
    imageUrl: "/placeholder.jpg",
    altText: "Prof. David Kim",
    name: "Prof. David Kim",
    title: "Assistant Professor, Business",
    department: "Business",
    email: "<EMAIL>",
    website: "https://faculty.college.edu/dkim",
    bio: "Finance expert with experience in investment banking and public policy, focusing on sustainable investment strategies.",
    education: [
      "Ph.D. Finance, London School of Economics",
      "MBA, INSEAD",
      "B.A. Economics, Seoul National University"
    ],
    research: [
      "Sustainable Finance",
      "ESG Investing",
      "Financial Markets and Policy"
    ],
    publications: [
      "Kim, D. (2022). 'ESG Factors in Investment Performance'. Journal of Sustainable Finance.",
      "Kim, D. & Brown, T. (2020). 'Green Bonds and Climate Finance'. Financial Markets Journal.",
      "Kim, D. (2018). 'Policy Implications for Sustainable Investment'. Journal of Financial Policy."
    ],
    courses: [
      "BUS-4300: Sustainable Finance",
      "BUS-3400: Investment Analysis",
      "BUS-2400: Financial Markets"
    ],
    officeHours: "Mondays and Wednesdays: 9:00 AM - 11:00 AM",
    office: "Business Building, Room 320"
  },
  {
    id: "maria-santos",
    imageUrl: "/placeholder.jpg",
    altText: "Dr. Maria Santos",
    name: "Dr. Maria Santos",
    title: "Associate Professor, Agriculture",
    department: "Agriculture and Climate Science",
    email: "<EMAIL>",
    website: "https://faculty.college.edu/msantos",
    bio: "Soil scientist with expertise in regenerative agriculture and carbon sequestration techniques for climate change mitigation.",
    education: [
      "Ph.D. Soil Science, University of São Paulo",
      "M.S. Agricultural Engineering, Texas A&M University",
      "B.S. Agronomy, Federal University of Rio de Janeiro"
    ],
    research: [
      "Soil Carbon Sequestration",
      "Regenerative Agriculture",
      "Climate-Smart Farming Practices"
    ],
    publications: [
      "Santos, M. et al. (2022). 'Carbon Sequestration Methods in Agriculture'. Climate Change Biology.",
      "Santos, M. & Lee, J. (2020). 'Soil Health Indicators for Sustainable Farming'. Soil Science Journal.",
      "Santos, M. (2018). 'Regenerative Practices for Tropical Soils'. Journal of Tropical Agriculture."
    ],
    courses: [
      "AGR-4200: Soil Science for Climate Mitigation",
      "AGR-3300: Regenerative Agriculture",
      "AGR-2200: Soil Biology"
    ],
    officeHours: "Tuesdays and Thursdays: 2:00 PM - 4:00 PM",
    office: "Agriculture Building, Room 230"
  },
  {
    id: "robert-taylor",
    imageUrl: "/placeholder.jpg",
    altText: "Prof. Robert Taylor",
    name: "Prof. Robert Taylor",
    title: "Associate Professor, Education",
    department: "Education",
    email: "<EMAIL>",
    website: "https://faculty.college.edu/rtaylor",
    bio: "Specialist in educational psychology and learning technologies with focus on creating accessible learning environments for diverse student populations.",
    education: [
      "Ph.D. Educational Psychology, University of Toronto",
      "M.Ed. Learning Design and Technology, Harvard University",
      "B.Ed. Secondary Education, University of British Columbia"
    ],
    research: [
      "Accessibility in Education",
      "Learning Technologies",
      "Universal Design for Learning"
    ],
    publications: [
      "Taylor, R. (2022). 'Universal Design Principles in Online Learning'. Educational Technology Research.",
      "Taylor, R. & Wong, S. (2020). 'Assistive Technologies in Classrooms'. Inclusive Education Journal.",
      "Taylor, R. (2018). 'Cognitive Load in Digital Learning Environments'. Learning Sciences Review."
    ],
    courses: [
      "EDU-4300: Accessible Learning Design",
      "EDU-3400: Educational Technology",
      "EDU-2300: Foundations of Learning Psychology"
    ],
    officeHours: "Wednesdays and Fridays: 1:00 PM - 3:00 PM",
    office: "Education Building, Room 315"
  }
];

// Define research areas with color coding
export const researchAreas: ResearchArea[] = [
  {
    name: "Artificial Intelligence and Machine Learning",
    facultyIds: ["sarah-johnson"],
    color: "#4C51BF" // Indigo
  },
  {
    name: "Neural Networks and Deep Learning",
    facultyIds: ["sarah-johnson"],
    color: "#667EEA" // Indigo lighter
  },
  {
    name: "Computer Vision and Pattern Recognition",
    facultyIds: ["sarah-johnson"],
    color: "#9F7AEA" // Purple
  },
  {
    name: "Cybersecurity",
    facultyIds: ["amira-patel"],
    color: "#ED64A6" // Pink
  },
  {
    name: "Network Security",
    facultyIds: ["amira-patel"],
    color: "#F687B3" // Pink lighter
  },
  {
    name: "Privacy-Preserving Computing",
    facultyIds: ["amira-patel"],
    color: "#FC8181" // Red lighter
  },
  {
    name: "Strategic Management",
    facultyIds: ["michael-chen"],
    color: "#F6AD55" // Orange
  },
  {
    name: "Entrepreneurship and Innovation",
    facultyIds: ["michael-chen"],
    color: "#F6E05E" // Yellow
  },
  {
    name: "Corporate Finance",
    facultyIds: ["michael-chen", "david-kim"],
    color: "#68D391" // Green
  },
  {
    name: "Sustainable Agriculture",
    facultyIds: ["emily-rodriguez"],
    color: "#38B2AC" // Teal
  },
  {
    name: "Climate Adaptation for Farming",
    facultyIds: ["emily-rodriguez"],
    color: "#4FD1C5" // Teal lighter
  },
  {
    name: "Agroecology and Biodiversity",
    facultyIds: ["emily-rodriguez"],
    color: "#81E6D9" // Teal lightest
  },
  {
    name: "Educational Technology",
    facultyIds: ["james-wilson", "robert-taylor"],
    color: "#63B3ED" // Blue
  },
  {
    name: "Teacher Professional Development",
    facultyIds: ["james-wilson"],
    color: "#7F9CF5" // Indigo lighter
  },
  {
    name: "Inclusive Education Practices",
    facultyIds: ["james-wilson", "robert-taylor"],
    color: "#B794F4" // Purple lighter
  },
  {
    name: "Sustainable Finance",
    facultyIds: ["david-kim"],
    color: "#48BB78" // Green
  },
  {
    name: "ESG Investing",
    facultyIds: ["david-kim"],
    color: "#68D391" // Green lighter
  },
  {
    name: "Financial Markets and Policy",
    facultyIds: ["david-kim"],
    color: "#9AE6B4" // Green lightest
  },
  {
    name: "Soil Carbon Sequestration",
    facultyIds: ["maria-santos"],
    color: "#38B2AC" // Teal
  },
  {
    name: "Regenerative Agriculture",
    facultyIds: ["maria-santos", "emily-rodriguez"],
    color: "#4FD1C5" // Teal lighter
  },
  {
    name: "Climate-Smart Farming Practices",
    facultyIds: ["maria-santos"],
    color: "#81E6D9" // Teal lightest
  },
  {
    name: "Accessibility in Education",
    facultyIds: ["robert-taylor"],
    color: "#7F9CF5" // Indigo lighter
  },
  {
    name: "Learning Technologies",
    facultyIds: ["robert-taylor"],
    color: "#B794F4" // Purple lighter
  },
  {
    name: "Universal Design for Learning",
    facultyIds: ["robert-taylor"],
    color: "#D6BCFA" // Purple lightest
  }
];

// Helper function to get faculty by ID from database
export async function getFacultyById(id: string): Promise<FacultyMember | null> {
  try {
    // Import prisma here to avoid circular dependencies
    const { prisma } = await import('@/lib/prisma')

    const user = await prisma.user.findUnique({
      where: {
        id: id,
        role: 'FACULTY',
        status: 'ACTIVE'
      },
      include: {
        profile: true,
        facultyProfile: {
          include: {
            department: true,
            publications: {
              orderBy: { year: 'desc' }
            },
            researchAreas: true,
            education: {
              orderBy: { year: 'desc' }
            },
            timeline: {
              orderBy: { year: 'desc' }
            }
          }
        }
      }
    })

    if (!user || !user.facultyProfile) {
      return null
    }

    // Transform database data to FacultyMember format
    const faculty: FacultyMember = {
      id: user.id,
      imageUrl: user.profile?.avatarUrl || "/placeholder.jpg",
      altText: user.name || "Faculty Member",
      name: user.name || "Faculty Member",
      title: user.facultyProfile.title || "Faculty Member",
      department: user.facultyProfile.department.name,
      email: user.email,
      website: user.facultyProfile.websiteUrl || "#",
      bio: user.facultyProfile.bio || "No bio available.",
      education: user.facultyProfile.education.map(edu =>
        `${edu.degree}, ${edu.institution}${edu.year ? ` (${edu.year})` : ''}`
      ),
      research: user.facultyProfile.researchAreas.map(area => area.areaName),
      publications: user.facultyProfile.publications.map(pub =>
        `${pub.authors.join(', ')} (${pub.year}). "${pub.title}". ${pub.journal}.`
      ),
      courses: [], // TODO: Add courses if needed
      officeHours: user.facultyProfile.officeHours || undefined,
      office: user.facultyProfile.office || undefined,
      // Extended data
      scholarlyPublications: user.facultyProfile.publications.map(pub => ({
        id: pub.id,
        title: pub.title,
        authors: pub.authors,
        journal: pub.journal,
        year: pub.year,
        citationCount: pub.citationCount,
        link: pub.link || "#",
        abstract: pub.abstract || undefined,
        tags: pub.tags
      })),
      timeline: user.facultyProfile.timeline.map(event => ({
        id: event.id,
        year: event.year,
        title: event.title,
        description: event.description,
        type: event.type.toLowerCase() as 'education' | 'position' | 'award'
      }))
    }

    return faculty
  } catch (error) {
    console.error('Error fetching faculty by ID:', error)
    return null
  }
}

// Legacy function for static data (keep for backward compatibility)
export function getFacultyByIdStatic(id: string): FacultyMember | null {
  return facultyData.find(faculty => faculty.id === id) || null;
}

// Helper function to get all research areas for a specific faculty member
export function getResearchAreasForFaculty(facultyId: string): ResearchArea[] {
  return researchAreas.filter(area => area.facultyIds.includes(facultyId));
}

// Helper function to search faculty by keyword
export function searchFaculty(query: string): FacultyMember[] {
  const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);

  if (searchTerms.length === 0) return facultyData;

  return facultyData.filter(faculty => {
    // Search in various faculty properties
    const searchableText = [
      faculty.name,
      faculty.title,
      faculty.department,
      faculty.bio,
      ...faculty.education,
      ...faculty.research,
      ...faculty.publications,
      ...(faculty.courses || [])
    ].join(' ').toLowerCase();

    // Check if all search terms are found
    return searchTerms.every(term => searchableText.includes(term));
  });
}

// Helper function to filter faculty by department
export function filterFacultyByDepartment(department: string): FacultyMember[] {
  if (!department) return facultyData;
  return facultyData.filter(faculty => faculty.department === department);
}

// Helper function to filter faculty by research area
export function filterFacultyByResearchArea(areaName: string): FacultyMember[] {
  if (!areaName) return facultyData;

  const area = researchAreas.find(a => a.name === areaName);
  if (!area) return [];

  return facultyData.filter(faculty => area.facultyIds.includes(faculty.id));
}

// Get unique departments
export function getUniqueDepartments(): string[] {
  return [...new Set(facultyData.map(f => f.department))];
}

// Get unique research area names
export function getUniqueResearchAreas(): string[] {
  return [...new Set(researchAreas.map(area => area.name))];
}