const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function fixPlaceholderImages() {
  console.log('🔍 Checking for placeholder image references in database...')
  
  try {
    // Check users with placeholder.jpg in image field
    const usersWithPlaceholder = await prisma.user.findMany({
      where: {
        OR: [
          { image: { contains: 'placeholder.jpg' } },
          { image: { contains: '/placeholder.jpg' } }
        ]
      },
      include: {
        profile: true,
        facultyProfile: true
      }
    })

    console.log(`Found ${usersWithPlaceholder.length} users with placeholder.jpg references`)

    if (usersWithPlaceholder.length > 0) {
      console.log('📝 Updating users with placeholder images...')
      
      for (const user of usersWithPlaceholder) {
        console.log(`  - Updating user: ${user.name || user.email} (${user.id})`)
        
        await prisma.user.update({
          where: { id: user.id },
          data: { image: null } // Remove placeholder reference
        })
      }
    }

    // Check user profiles with placeholder.jpg in avatarUrl field
    const profilesWithPlaceholder = await prisma.userProfile.findMany({
      where: {
        OR: [
          { avatarUrl: { contains: 'placeholder.jpg' } },
          { avatarUrl: { contains: '/placeholder.jpg' } }
        ]
      },
      include: {
        user: true
      }
    })

    console.log(`Found ${profilesWithPlaceholder.length} user profiles with placeholder.jpg references`)

    if (profilesWithPlaceholder.length > 0) {
      console.log('📝 Updating user profiles with placeholder images...')
      
      for (const profile of profilesWithPlaceholder) {
        console.log(`  - Updating profile for: ${profile.user.name || profile.user.email} (${profile.id})`)
        
        await prisma.userProfile.update({
          where: { id: profile.id },
          data: { avatarUrl: null } // Remove placeholder reference
        })
      }
    }

    console.log('✅ Database cleanup completed!')
    
    // Show summary of faculty users and their image sources
    const facultyUsers = await prisma.user.findMany({
      where: {
        role: 'FACULTY',
        status: 'ACTIVE'
      },
      include: {
        profile: true,
        facultyProfile: true
      },
      take: 10
    })

    console.log('\n📊 Current faculty image sources:')
    facultyUsers.forEach(user => {
      const imageSource = user.profile?.avatarUrl || user.image || 'default-avatar.svg'
      console.log(`  - ${user.name || user.email}: ${imageSource}`)
    })

  } catch (error) {
    console.error('❌ Error during database cleanup:', error)
  } finally {
    await prisma.$disconnect()
  }
}

fixPlaceholderImages()
